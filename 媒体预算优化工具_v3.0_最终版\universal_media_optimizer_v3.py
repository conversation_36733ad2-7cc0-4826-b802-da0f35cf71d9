import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import json
import os
import win32com.client
import time
from datetime import datetime
import threading

class UniversalMediaOptimizerV2:
    def __init__(self, root):
        self.root = root
        self.root.title("通用媒体预算优化工具 v3.0 - 增强版")
        # 适配16:9屏幕比例，使用更宽的窗口
        self.root.geometry("1400x900")
        self.root.minsize(1200, 700)  # 设置最小尺寸
        
        # 配置变量
        self.config = {
            "excel_file": "",
            "worksheet_name": "",  # 移除默认值
            "input_start_row": 1,  # 移除默认值
            "input_end_row": 1,    # 移除默认值
            "input_start_col": 1,  # 移除默认值，将改为字母输入
            "input_end_col": 1,    # 移除默认值，将改为字母输入
            "output_col": 1,       # 移除默认值，将改为字母输入
            "output_start_row": 1,  # 移除默认值
            "output_end_row": 1,    # 移除默认值
            "channel_names_row": 1,
            "total_budget": 1000000,
            "optimization_schemes": 5,
            "max_iterations": 200,  # 迭代次数
            "baseline_allocation": "equal",  # equal, custom
            "custom_baseline_ratios": {},  # 自定义基准配比
            "channel_constraints": {},
            "kpi_type": "Non-BHT"  # 新增KPI类型选择：Non-BHT（求和）或BHT（平均）
        }
        
        self.channels = []
        self.excel = None
        self.workbook = None
        self.worksheet = None
        self.optimizer_engine = None  # 优化引擎实例

        self.create_widgets()
        self.load_config()

    def col_num_to_letter(self, col_num):
        """将列数字转换为字母（如1->A, 2->B, 27->AA）"""
        if col_num <= 0:
            return "A"
        result = ""
        while col_num > 0:
            col_num -= 1
            result = chr(col_num % 26 + ord('A')) + result
            col_num //= 26
        return result

    def col_letter_to_num(self, col_letter):
        """将列字母转换为数字（如A->1, B->2, AA->27）"""
        if not col_letter:
            return 1
        col_letter = col_letter.upper()
        result = 0
        for char in col_letter:
            if 'A' <= char <= 'Z':
                result = result * 26 + (ord(char) - ord('A') + 1)
            else:
                return 1  # 无效字符时返回默认值
        return result
    
    def create_widgets(self):
        """创建界面组件 - 16:9布局优化"""
        # 创建主容器，使用左右分栏布局
        main_container = ttk.Frame(self.root, padding="10")
        main_container.pack(fill=tk.BOTH, expand=True)

        # 左侧面板 - 配置区域
        left_panel = ttk.Frame(main_container)
        left_panel.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))

        # 右侧面板 - 监控和结果区域
        right_panel = ttk.Frame(main_container)
        right_panel.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # 左侧滚动框架
        left_canvas = tk.Canvas(left_panel)
        left_scrollbar = ttk.Scrollbar(left_panel, orient="vertical", command=left_canvas.yview)
        left_scrollable_frame = ttk.Frame(left_canvas)

        left_scrollable_frame.bind(
            "<Configure>",
            lambda e: left_canvas.configure(scrollregion=left_canvas.bbox("all"))
        )

        left_canvas.create_window((0, 0), window=left_scrollable_frame, anchor="nw")
        left_canvas.configure(yscrollcommand=left_scrollbar.set)

        # 左侧配置区域
        config_frame = ttk.Frame(left_scrollable_frame, padding="5")
        config_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 1. 文件设置区域
        file_frame = ttk.LabelFrame(config_frame, text="1. 文件设置", padding="10")
        file_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)

        ttk.Label(file_frame, text="Excel文件:").grid(row=0, column=0, sticky=tk.W)
        self.file_var = tk.StringVar(value=self.config["excel_file"])
        ttk.Entry(file_frame, textvariable=self.file_var, width=50).grid(row=0, column=1, padx=5)  # 调整宽度适配左侧面板
        ttk.Button(file_frame, text="浏览", command=self.browse_file).grid(row=0, column=2)

        ttk.Label(file_frame, text="工作表名称:").grid(row=1, column=0, sticky=tk.W)
        self.worksheet_var = tk.StringVar(value=self.config["worksheet_name"])
        ttk.Entry(file_frame, textvariable=self.worksheet_var, width=20).grid(row=1, column=1, sticky=tk.W, padx=5)

        # 2. 数据区域设置
        area_frame = ttk.LabelFrame(config_frame, text="2. 数据区域设置", padding="10")
        area_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        # 输入区域
        ttk.Label(area_frame, text="输入区域:").grid(row=0, column=0, sticky=tk.W)
        input_frame = ttk.Frame(area_frame)
        input_frame.grid(row=0, column=1, sticky=tk.W, padx=5)
        
        ttk.Label(input_frame, text="行:").grid(row=0, column=0)
        self.start_row_var = tk.IntVar(value=self.config["input_start_row"])
        ttk.Entry(input_frame, textvariable=self.start_row_var, width=5).grid(row=0, column=1)
        ttk.Label(input_frame, text="到").grid(row=0, column=2)
        self.end_row_var = tk.IntVar(value=self.config["input_end_row"])
        ttk.Entry(input_frame, textvariable=self.end_row_var, width=5).grid(row=0, column=3)
        
        ttk.Label(input_frame, text="列:").grid(row=0, column=4, padx=(10,0))
        self.start_col_var = tk.StringVar(value=self.col_num_to_letter(self.config["input_start_col"]))
        ttk.Entry(input_frame, textvariable=self.start_col_var, width=5).grid(row=0, column=5)
        ttk.Label(input_frame, text="到").grid(row=0, column=6)
        self.end_col_var = tk.StringVar(value=self.col_num_to_letter(self.config["input_end_col"]))
        ttk.Entry(input_frame, textvariable=self.end_col_var, width=5).grid(row=0, column=7)
        
        # 输出区域
        ttk.Label(area_frame, text="输出区域:").grid(row=1, column=0, sticky=tk.W)
        output_frame = ttk.Frame(area_frame)
        output_frame.grid(row=1, column=1, sticky=tk.W, padx=5)
        
        ttk.Label(output_frame, text="行:").grid(row=0, column=0)
        self.output_start_row_var = tk.IntVar(value=self.config["output_start_row"])
        ttk.Entry(output_frame, textvariable=self.output_start_row_var, width=5).grid(row=0, column=1)
        ttk.Label(output_frame, text="到").grid(row=0, column=2)
        self.output_end_row_var = tk.IntVar(value=self.config["output_end_row"])
        ttk.Entry(output_frame, textvariable=self.output_end_row_var, width=5).grid(row=0, column=3)
        
        ttk.Label(output_frame, text="列:").grid(row=0, column=4, padx=(10,0))
        self.output_col_var = tk.StringVar(value=self.col_num_to_letter(self.config["output_col"]))
        ttk.Entry(output_frame, textvariable=self.output_col_var, width=5).grid(row=0, column=5)
        
        # 同步按钮
        ttk.Button(output_frame, text="同步输入区域", 
                  command=self.sync_output_with_input).grid(row=0, column=6, padx=(10,0))
        
        # 渠道名称行
        ttk.Label(area_frame, text="渠道名称行:").grid(row=2, column=0, sticky=tk.W)
        self.channel_row_var = tk.IntVar(value=self.config["channel_names_row"])
        ttk.Entry(area_frame, textvariable=self.channel_row_var, width=5).grid(row=2, column=1, sticky=tk.W, padx=5)

        # KPI类型选择
        ttk.Label(area_frame, text="KPI类型:").grid(row=3, column=0, sticky=tk.W)
        self.kpi_type_var = tk.StringVar(value=self.config["kpi_type"])
        kpi_type_frame = ttk.Frame(area_frame)
        kpi_type_frame.grid(row=3, column=1, sticky=tk.W, padx=5)

        ttk.Radiobutton(kpi_type_frame, text="Non-BHT (求和)", variable=self.kpi_type_var,
                       value="Non-BHT").grid(row=0, column=0, sticky=tk.W)
        ttk.Radiobutton(kpi_type_frame, text="BHT (平均)", variable=self.kpi_type_var,
                       value="BHT").grid(row=0, column=1, sticky=tk.W, padx=(20,0))

        # 添加KPI类型说明
        kpi_info_label = ttk.Label(area_frame, text="💡 Non-BHT: 各行KPI求和 | BHT: 各行KPI平均值",
                                  foreground="blue", font=("Arial", 8))
        kpi_info_label.grid(row=4, column=0, columnspan=2, sticky=tk.W, pady=(5,0))
        
        # 3. 预算和优化设置
        budget_frame = ttk.LabelFrame(config_frame, text="3. 预算和优化设置", padding="15")
        budget_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)

        # 第一行：预算设置
        ttk.Label(budget_frame, text="总预算:", font=("Arial", 9, "bold")).grid(row=0, column=0, sticky=tk.W, pady=2)
        self.budget_var = tk.IntVar(value=self.config["total_budget"])
        budget_entry = ttk.Entry(budget_frame, textvariable=self.budget_var, width=15, font=("Arial", 9))
        budget_entry.grid(row=0, column=1, sticky=tk.W, padx=(5,20), pady=2)
        ttk.Label(budget_frame, text="元", foreground="gray").grid(row=0, column=2, sticky=tk.W, pady=2)

        # 第二行：优化参数
        ttk.Label(budget_frame, text="优化方案数量:", font=("Arial", 9, "bold")).grid(row=1, column=0, sticky=tk.W, pady=2)
        self.schemes_var = tk.IntVar(value=self.config["optimization_schemes"])
        schemes_entry = ttk.Entry(budget_frame, textvariable=self.schemes_var, width=8, font=("Arial", 9))
        schemes_entry.grid(row=1, column=1, sticky=tk.W, padx=(5,20), pady=2)
        ttk.Label(budget_frame, text="个", foreground="gray").grid(row=1, column=2, sticky=tk.W, pady=2)

        ttk.Label(budget_frame, text="迭代次数:", font=("Arial", 9, "bold")).grid(row=1, column=3, sticky=tk.W, padx=(30,0), pady=2)
        self.iterations_var = tk.IntVar(value=self.config.get("max_iterations", 200))
        iterations_entry = ttk.Entry(budget_frame, textvariable=self.iterations_var, width=8, font=("Arial", 9))
        iterations_entry.grid(row=1, column=4, sticky=tk.W, padx=(5,20), pady=2)
        ttk.Label(budget_frame, text="次", foreground="gray").grid(row=1, column=5, sticky=tk.W, pady=2)

        # 第三行：基准方案设置
        ttk.Label(budget_frame, text="基准方案:", font=("Arial", 9, "bold")).grid(row=2, column=0, sticky=tk.W, pady=2)
        self.baseline_var = tk.StringVar(value=self.config["baseline_allocation"])
        baseline_combo = ttk.Combobox(budget_frame, textvariable=self.baseline_var,
                                    values=["equal", "custom"], width=12, font=("Arial", 9))
        baseline_combo.grid(row=2, column=1, sticky=tk.W, padx=(5,20), pady=2)
        baseline_combo.bind("<<ComboboxSelected>>", self.on_baseline_changed)

        self.custom_baseline_btn = ttk.Button(budget_frame, text="设置自定义配比",
                                            command=self.set_custom_baseline, state="disabled")
        self.custom_baseline_btn.grid(row=2, column=3, columnspan=2, sticky=tk.W, padx=(30,0), pady=2)

        # 添加自动保存功能
        self.schemes_var.trace_add('write', self.on_schemes_changed)
        self.iterations_var.trace_add('write', self.on_iterations_changed)

        # 4. 输出设置
        output_frame = ttk.LabelFrame(config_frame, text="4. 输出设置", padding="15")
        output_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)

        # 输出目录设置
        ttk.Label(output_frame, text="输出目录:", font=("Arial", 9, "bold")).grid(row=0, column=0, sticky=tk.W, pady=2)
        self.output_dir_var = tk.StringVar(value=self.config.get("output_directory", os.getcwd()))
        output_dir_entry = ttk.Entry(output_frame, textvariable=self.output_dir_var, width=40, font=("Arial", 9))  # 调整宽度
        output_dir_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(5,10), pady=2)
        ttk.Button(output_frame, text="📁 浏览", command=self.browse_output_directory).grid(row=0, column=2, padx=5, pady=2)

        # 文件名前缀设置
        ttk.Label(output_frame, text="文件名前缀:", font=("Arial", 9, "bold")).grid(row=1, column=0, sticky=tk.W, pady=2)
        self.output_prefix_var = tk.StringVar(value=self.config.get("output_prefix", "媒体预算优化结果"))
        output_prefix_entry = ttk.Entry(output_frame, textvariable=self.output_prefix_var, width=25, font=("Arial", 9))  # 调整宽度
        output_prefix_entry.grid(row=1, column=1, sticky=tk.W, padx=(5,10), pady=2)
        ttk.Label(output_frame, text="💡 支持中文名称", foreground="blue", font=("Arial", 8)).grid(row=1, column=2, sticky=tk.W, padx=5, pady=2)

        # 配置列权重，使输入框可以拉伸
        output_frame.columnconfigure(1, weight=1)

        # 添加自动保存功能
        self.output_dir_var.trace_add('write', self.on_output_settings_changed)
        self.output_prefix_var.trace_add('write', self.on_output_settings_changed)
        
        # 5. 渠道约束设置
        constraint_frame = ttk.LabelFrame(config_frame, text="5. 渠道约束设置", padding="15")
        constraint_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        button_constraint_frame = ttk.Frame(constraint_frame)
        button_constraint_frame.grid(row=0, column=0, columnspan=3, sticky=(tk.W, tk.E))
        
        ttk.Button(button_constraint_frame, text="读取渠道信息", 
                  command=self.load_channels).grid(row=0, column=0, pady=5)
        ttk.Button(button_constraint_frame, text="设置渠道约束", 
                  command=self.set_channel_constraints).grid(row=0, column=1, padx=5, pady=5)
        
        # 默认约束说明
        ttk.Label(button_constraint_frame, text="默认约束范围: 2%-60% (可修改)", 
                 foreground="blue").grid(row=0, column=2, padx=10)
        
        # 渠道信息显示
        self.channel_text = tk.Text(constraint_frame, height=8, width=60)  # 调整尺寸适配左侧面板
        self.channel_text.grid(row=1, column=0, columnspan=2, pady=5)
        
        channel_scrollbar = ttk.Scrollbar(constraint_frame, orient="vertical", command=self.channel_text.yview)
        channel_scrollbar.grid(row=1, column=2, sticky=(tk.N, tk.S))
        self.channel_text.configure(yscrollcommand=channel_scrollbar.set)
        
        # 6. 操作按钮
        button_frame = ttk.LabelFrame(config_frame, text="6. 操作控制", padding="15")
        button_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=10)

        # 第一行：配置操作
        config_frame = ttk.LabelFrame(button_frame, text="配置管理", padding="10")
        config_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)

        ttk.Button(config_frame, text="📊 读取渠道", command=self.load_channels, width=12).grid(row=0, column=0, padx=5, pady=2)
        ttk.Button(config_frame, text="⚙️ 设置约束", command=self.set_channel_constraints, width=12).grid(row=0, column=1, padx=5, pady=2)
        ttk.Button(config_frame, text="💾 保存配置", command=self.save_config, width=12).grid(row=0, column=2, padx=5, pady=2)
        ttk.Button(config_frame, text="📂 加载配置", command=self.load_config_file, width=12).grid(row=0, column=3, padx=5, pady=2)

        # 第二行：优化控制
        control_frame = ttk.LabelFrame(button_frame, text="优化控制", padding="10")
        control_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)

        self.start_btn = ttk.Button(control_frame, text="🚀 开始优化", command=self.start_optimization, width=15)
        self.start_btn.grid(row=0, column=0, padx=10, pady=5)

        self.pause_btn = ttk.Button(control_frame, text="⏸️ 暂停", command=self.pause_optimization, state="disabled", width=12)
        self.pause_btn.grid(row=0, column=1, padx=5, pady=5)

        self.resume_btn = ttk.Button(control_frame, text="▶️ 恢复", command=self.resume_optimization, state="disabled", width=12)
        self.resume_btn.grid(row=0, column=2, padx=5, pady=5)

        self.stop_btn = ttk.Button(control_frame, text="⏹️ 终止", command=self.terminate_optimization, state="disabled", width=12)
        self.stop_btn.grid(row=0, column=3, padx=5, pady=5)

        # 左侧滚动配置
        left_canvas.pack(side="left", fill="both", expand=True)
        left_scrollbar.pack(side="right", fill="y")

        # 绑定鼠标滚轮到左侧面板
        def _on_left_mousewheel(event):
            left_canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        left_canvas.bind_all("<MouseWheel>", _on_left_mousewheel)

        # === 右侧面板内容 ===
        # 7. 实时监控窗口
        monitor_frame = ttk.LabelFrame(right_panel, text="📊 实时监控仪表板", padding="15")
        monitor_frame.pack(fill=tk.X, pady=(0, 10))

        # 创建监控网格布局
        monitor_grid = ttk.Frame(monitor_frame)
        monitor_grid.pack(fill=tk.X)

        # 第一行：KPI指标
        kpi_row = ttk.Frame(monitor_grid)
        kpi_row.pack(fill=tk.X, pady=5)

        # 基准KPI卡片
        baseline_card = ttk.LabelFrame(kpi_row, text="📈 基准KPI", padding="10")
        baseline_card.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))

        self.baseline_kpi_var = tk.StringVar(value="未计算")
        baseline_value = ttk.Label(baseline_card, textvariable=self.baseline_kpi_var,
                                  font=("Arial", 12, "bold"), foreground="#0066CC")
        baseline_value.pack()

        # 当前最优KPI卡片
        best_card = ttk.LabelFrame(kpi_row, text="🏆 当前最优KPI", padding="10")
        best_card.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

        self.best_kpi_var = tk.StringVar(value="未计算")
        best_value = ttk.Label(best_card, textvariable=self.best_kpi_var,
                              font=("Arial", 12, "bold"), foreground="#008000")
        best_value.pack()

        # 提升幅度卡片
        improvement_card = ttk.LabelFrame(kpi_row, text="📊 提升幅度", padding="10")
        improvement_card.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 0))

        self.improvement_var = tk.StringVar(value="0.00%")
        improvement_value = ttk.Label(improvement_card, textvariable=self.improvement_var,
                                     font=("Arial", 12, "bold"), foreground="#FF6600")
        improvement_value.pack()

        # 第二行：状态指标
        status_row = ttk.Frame(monitor_grid)
        status_row.pack(fill=tk.X, pady=5)

        # 优化状态卡片
        status_card = ttk.LabelFrame(status_row, text="⚡ 优化状态", padding="10")
        status_card.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))

        self.optimization_status_var = tk.StringVar(value="🟢 就绪")
        status_label = ttk.Label(status_card, textvariable=self.optimization_status_var,
                                font=("Arial", 10, "bold"))
        status_label.pack()

        # 迭代进度卡片
        iteration_card = ttk.LabelFrame(status_row, text="🔄 迭代进度", padding="10")
        iteration_card.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

        self.iteration_var = tk.StringVar(value="0/0")
        iteration_label = ttk.Label(iteration_card, textvariable=self.iteration_var,
                                   font=("Arial", 10, "bold"), foreground="#9900CC")
        iteration_label.pack()

        # 方案数量卡片
        schemes_card = ttk.LabelFrame(status_row, text="📋 生成方案", padding="10")
        schemes_card.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 0))

        self.schemes_count_var = tk.StringVar(value="0 个")
        schemes_label = ttk.Label(schemes_card, textvariable=self.schemes_count_var,
                                 font=("Arial", 10, "bold"), foreground="#CC6600")
        schemes_label.pack()

        # 第三行：实时日志
        log_frame = ttk.LabelFrame(monitor_grid, text="📝 实时日志", padding="10")
        log_frame.pack(fill=tk.X, pady=(10, 0))

        # 创建日志文本框
        log_container = ttk.Frame(log_frame)
        log_container.pack(fill=tk.X)

        self.log_text = tk.Text(log_container, height=6, width=50, wrap=tk.WORD,
                               font=("Consolas", 9), bg="#F8F9FA")
        log_scrollbar = ttk.Scrollbar(log_container, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)

        self.log_text.pack(side="left", fill="both", expand=True)
        log_scrollbar.pack(side="right", fill="y")

        # 初始化日志
        self.add_log("📊 监控系统已启动")
        self.add_log("💡 配置完成后点击'开始优化'")
        self.log_text.config(state=tk.DISABLED)

        # 8. 优化进度
        progress_frame = ttk.LabelFrame(right_panel, text="8. 优化进度", padding="15")
        progress_frame.pack(fill=tk.X, pady=(0, 10))

        # 进度条
        progress_label = ttk.Label(progress_frame, text="优化进度:", font=("Arial", 9, "bold"))
        progress_label.grid(row=0, column=0, sticky=tk.W, pady=2)

        self.progress = ttk.Progressbar(progress_frame, mode='determinate', length=400)
        self.progress.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10,0), pady=2)

        # 状态信息
        status_label = ttk.Label(progress_frame, text="当前状态:", font=("Arial", 9, "bold"))
        status_label.grid(row=1, column=0, sticky=tk.W, pady=2)

        self.status_var = tk.StringVar(value="🟢 就绪")
        self.status_label = ttk.Label(progress_frame, textvariable=self.status_var, font=("Arial", 9))
        self.status_label.grid(row=1, column=1, sticky=tk.W, padx=(10,0), pady=2)

        # 详细进度信息
        detail_label = ttk.Label(progress_frame, text="详细信息:", font=("Arial", 9, "bold"))
        detail_label.grid(row=2, column=0, sticky=tk.W, pady=2)

        self.progress_detail_var = tk.StringVar(value="等待开始优化...")
        self.progress_detail_label = ttk.Label(progress_frame, textvariable=self.progress_detail_var,
                                              font=("Arial", 9), foreground="blue")
        self.progress_detail_label.grid(row=2, column=1, sticky=tk.W, padx=(10,0), pady=2)

        # 配置列权重
        progress_frame.columnconfigure(1, weight=1)

        # 9. 结果预览区域
        preview_frame = ttk.LabelFrame(right_panel, text="9. 结果预览", padding="15")
        preview_frame.pack(fill=tk.BOTH, expand=True)

        # 结果预览文本框
        self.preview_text = tk.Text(preview_frame, height=15, width=50, wrap=tk.WORD)
        preview_scrollbar = ttk.Scrollbar(preview_frame, orient="vertical", command=self.preview_text.yview)
        self.preview_text.configure(yscrollcommand=preview_scrollbar.set)

        self.preview_text.pack(side="left", fill="both", expand=True)
        preview_scrollbar.pack(side="right", fill="y")

        # 初始化预览内容
        self.preview_text.insert(tk.END, "📊 优化结果预览\n\n")
        self.preview_text.insert(tk.END, "等待开始优化...\n\n")
        self.preview_text.insert(tk.END, "💡 提示：\n")
        self.preview_text.insert(tk.END, "• 配置完成后点击'开始优化'\n")
        self.preview_text.insert(tk.END, "• 优化过程中可实时查看进度\n")
        self.preview_text.insert(tk.END, "• 完成后可在此预览结果摘要\n")
        self.preview_text.config(state=tk.DISABLED)  # 设为只读
    
    def sync_output_with_input(self):
        """同步输出区域与输入区域"""
        self.output_start_row_var.set(self.start_row_var.get())
        self.output_end_row_var.set(self.end_row_var.get())
    
    def on_baseline_changed(self, event=None):
        """基准方案选择改变时的回调"""
        if self.baseline_var.get() == "custom":
            self.custom_baseline_btn.config(state="normal")
        else:
            self.custom_baseline_btn.config(state="disabled")

    def on_iterations_changed(self, *args):
        """迭代次数改变时的回调 - 自动更新配置"""
        try:
            # 实时更新配置中的迭代次数
            self.config["max_iterations"] = self.iterations_var.get()
            print(f"迭代次数已更新为: {self.config['max_iterations']}")
        except:
            pass  # 忽略输入错误

    def on_schemes_changed(self, *args):
        """方案数量改变时的回调 - 自动更新配置"""
        try:
            # 实时更新配置中的方案数量
            self.config["optimization_schemes"] = self.schemes_var.get()
            print(f"优化方案数量已更新为: {self.config['optimization_schemes']}")
        except:
            pass  # 忽略输入错误

    def on_output_settings_changed(self, *args):
        """输出设置改变时的回调 - 自动更新配置"""
        try:
            # 实时更新配置中的输出设置
            self.config["output_directory"] = self.output_dir_var.get()
            self.config["output_prefix"] = self.output_prefix_var.get()
            print(f"输出目录已更新为: {self.config['output_directory']}")
            print(f"文件名前缀已更新为: {self.config['output_prefix']}")
        except:
            pass  # 忽略输入错误

    def browse_output_directory(self):
        """浏览输出目录"""
        directory = filedialog.askdirectory(
            title="选择输出目录",
            initialdir=self.output_dir_var.get()
        )
        if directory:
            self.output_dir_var.set(directory)
            print(f"输出目录设置为: {directory}")
    
    def set_custom_baseline(self):
        """设置自定义基准配比"""
        if not self.channels:
            messagebox.showwarning("警告", "请先读取渠道信息")
            return
        
        # 创建自定义基准设置窗口
        custom_window = tk.Toplevel(self.root)
        custom_window.title("设置自定义基准配比 - 滑块版")
        custom_window.geometry("700x700")

        ttk.Label(custom_window, text="🎛️ 使用滑块设置各渠道的基准预算配比（总和应为100%）",
                 font=("Arial", 12, "bold")).pack(pady=10)
        
        # 创建滚动框架
        canvas = tk.Canvas(custom_window)
        scrollbar = ttk.Scrollbar(custom_window, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 渠道配比滑块输入
        ratio_vars = {}
        ratio_labels = {}

        for i, channel in enumerate(self.channels):
            frame = ttk.Frame(scrollable_frame)
            frame.pack(fill=tk.X, padx=10, pady=5)

            # 渠道名称标签
            name_label = ttk.Label(frame, text=f"{channel}:", width=20, font=("Arial", 9, "bold"))
            name_label.pack(side=tk.LEFT, anchor=tk.W)

            # 获取已保存的配比或默认值
            default_ratio = self.config["custom_baseline_ratios"].get(channel, 100.0 / len(self.channels))
            ratio_var = tk.DoubleVar(value=default_ratio)
            ratio_vars[channel] = ratio_var

            # 滑块容器
            slider_frame = ttk.Frame(frame)
            slider_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=10)

            # 滑块
            slider = ttk.Scale(slider_frame, from_=0, to=50, orient=tk.HORIZONTAL,
                              variable=ratio_var, length=300)
            slider.pack(side=tk.LEFT, fill=tk.X, expand=True)

            # 数值显示标签
            value_label = ttk.Label(frame, text=f"{default_ratio:.1f}%", width=8,
                                   font=("Arial", 9), foreground="blue")
            value_label.pack(side=tk.LEFT, padx=5)
            ratio_labels[channel] = value_label

            # 精确输入框（小）
            entry_frame = ttk.Frame(frame)
            entry_frame.pack(side=tk.LEFT, padx=5)

            ttk.Label(entry_frame, text="精确:", font=("Arial", 8)).pack(side=tk.LEFT)
            entry = ttk.Entry(entry_frame, textvariable=ratio_var, width=6, font=("Arial", 8))
            entry.pack(side=tk.LEFT, padx=2)
            ttk.Label(entry_frame, text="%", font=("Arial", 8)).pack(side=tk.LEFT)
        
        canvas.pack(side="left", fill="both", expand=True, padx=10, pady=10)
        scrollbar.pack(side="right", fill="y")

        # 实时更新函数
        def update_display():
            """实时更新显示和总配比"""
            total = 0
            for channel, var in ratio_vars.items():
                value = var.get()
                total += value
                ratio_labels[channel].config(text=f"{value:.1f}%")

            # 更新总配比显示
            total_label.config(text=f"总配比: {total:.1f}%",
                             foreground="green" if abs(total - 100) < 0.1 else "red")

            # 如果总配比超过100%，自动调整其他滑块
            if total > 100:
                excess = total - 100
                # 找到当前变化的滑块，调整其他滑块
                for ch, var in ratio_vars.items():
                    if var.get() > 0 and excess > 0:
                        reduction = min(var.get(), excess / len(ratio_vars))
                        var.set(max(0, var.get() - reduction))
                        excess -= reduction

        # 为每个滑块绑定实时更新
        for channel, var in ratio_vars.items():
            var.trace_add('write', lambda *args: update_display())

        # 按钮框架
        button_frame = ttk.Frame(custom_window)
        button_frame.pack(fill=tk.X, padx=10, pady=10)

        def check_total():
            """检查总配比"""
            update_display()
        
        def save_custom_baseline():
            """保存自定义基准配比"""
            total = sum(var.get() for var in ratio_vars.values())
            if abs(total - 100) > 0.1:
                messagebox.showerror("错误", f"总配比必须为100%，当前为{total:.1f}%")
                return
            
            # 保存配比
            self.config["custom_baseline_ratios"] = {
                channel: var.get() for channel, var in ratio_vars.items()
            }
            
            messagebox.showinfo("成功", "自定义基准配比已保存")
            custom_window.destroy()
        
        def auto_equal():
            """自动平均分配"""
            equal_ratio = 100.0 / len(self.channels)
            for var in ratio_vars.values():
                var.set(equal_ratio)

        def reset_all():
            """重置所有滑块为0"""
            for var in ratio_vars.values():
                var.set(0)

        def smart_distribute():
            """智能分配：数字媒体40%，社交媒体30%，其他平分"""
            reset_all()
            digital_channels = [ch for ch in self.channels if any(keyword in ch.lower()
                               for keyword in ['搜索', '信息流', 'sem', 'seo', '程序化'])]
            social_channels = [ch for ch in self.channels if any(keyword in ch.lower()
                              for keyword in ['微信', '微博', '抖音', '小红书', 'social'])]

            if digital_channels:
                digital_ratio = 40.0 / len(digital_channels)
                for ch in digital_channels:
                    if ch in ratio_vars:
                        ratio_vars[ch].set(digital_ratio)

            if social_channels:
                social_ratio = 30.0 / len(social_channels)
                for ch in social_channels:
                    if ch in ratio_vars:
                        ratio_vars[ch].set(social_ratio)

            # 其他渠道平分剩余30%
            other_channels = [ch for ch in self.channels if ch not in digital_channels and ch not in social_channels]
            if other_channels:
                other_ratio = 30.0 / len(other_channels)
                for ch in other_channels:
                    ratio_vars[ch].set(other_ratio)

        # 预设按钮区域
        preset_frame = ttk.LabelFrame(button_frame, text="快速预设", padding="5")
        preset_frame.pack(fill=tk.X, pady=5)

        ttk.Button(preset_frame, text="🟰 平均分配", command=auto_equal).pack(side=tk.LEFT, padx=5)
        ttk.Button(preset_frame, text="🧠 智能分配", command=smart_distribute).pack(side=tk.LEFT, padx=5)
        ttk.Button(preset_frame, text="🔄 重置清零", command=reset_all).pack(side=tk.LEFT, padx=5)

        # 操作按钮区域
        action_frame = ttk.Frame(button_frame)
        action_frame.pack(fill=tk.X, pady=5)

        ttk.Button(action_frame, text="💾 保存配比", command=save_custom_baseline).pack(side=tk.LEFT, padx=5)
        ttk.Button(action_frame, text="❌ 取消", command=custom_window.destroy).pack(side=tk.RIGHT, padx=5)
        
        # 总配比显示
        total_label = ttk.Label(action_frame, text="总配比: 0.0%", font=("Arial", 10, "bold"))
        total_label.pack(side=tk.RIGHT, padx=10)

        # 初始化显示
        update_display()
    
    def browse_file(self):
        """浏览选择Excel文件"""
        filename = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[("Excel files", "*.xlsx *.xls"), ("All files", "*.*")]
        )
        if filename:
            self.file_var.set(filename)

    def load_channels(self):
        """读取渠道信息"""
        try:
            excel_file = self.file_var.get()
            if not excel_file or not os.path.exists(excel_file):
                messagebox.showerror("错误", "请先选择有效的Excel文件")
                return

            self.status_var.set("正在读取渠道信息...")

            # 初始化Excel
            excel = win32com.client.Dispatch("Excel.Application")
            excel.Visible = False
            excel.DisplayAlerts = False

            try:
                workbook = excel.Workbooks.Open(os.path.abspath(excel_file))
                worksheet = workbook.Worksheets(self.worksheet_var.get())

                # 读取渠道名称
                self.channels = []
                start_col = self.col_letter_to_num(self.start_col_var.get())
                end_col = self.col_letter_to_num(self.end_col_var.get())
                channel_row = self.channel_row_var.get()

                for col in range(start_col, end_col + 1):
                    cell_value = worksheet.Cells(channel_row, col).Value
                    if cell_value:
                        self.channels.append(str(cell_value).strip())

                # 显示渠道信息
                self.channel_text.delete(1.0, tk.END)
                self.channel_text.insert(tk.END, f"成功读取到 {len(self.channels)} 个媒体渠道:\n\n")
                for i, channel in enumerate(self.channels, 1):
                    constraint_info = ""
                    if channel in self.config["channel_constraints"]:
                        constraint = self.config["channel_constraints"][channel]
                        constraint_info = f" [约束: {constraint['min']:.1%}-{constraint['max']:.1%}]"

                    custom_info = ""
                    if channel in self.config["custom_baseline_ratios"]:
                        ratio = self.config["custom_baseline_ratios"][channel]
                        custom_info = f" [自定义基准: {ratio:.1f}%]"

                    self.channel_text.insert(tk.END, f"{i:2d}. {channel}{constraint_info}{custom_info}\n")

                self.status_var.set(f"成功读取 {len(self.channels)} 个渠道")

            finally:
                workbook.Close(SaveChanges=False)
                excel.Quit()

        except Exception as e:
            messagebox.showerror("错误", f"读取渠道信息失败: {str(e)}")
            self.status_var.set("读取失败")

    def set_channel_constraints(self):
        """设置渠道约束 - 可编辑表格版本"""
        if not self.channels:
            messagebox.showwarning("警告", "请先读取渠道信息")
            return

        # 创建约束设置窗口
        constraint_window = tk.Toplevel(self.root)
        constraint_window.title("设置渠道约束")
        constraint_window.geometry("800x700")

        # 说明文字
        info_frame = ttk.Frame(constraint_window)
        info_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(info_frame, text="💡 提示：双击单元格可直接编辑，支持批量操作",
                 font=("Arial", 10), foreground="blue").pack(anchor=tk.W)

        # 创建可编辑表格框架
        table_frame = ttk.Frame(constraint_window)
        table_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # 表头
        header_frame = ttk.Frame(table_frame)
        header_frame.pack(fill=tk.X)

        ttk.Label(header_frame, text="渠道名称", width=20, font=("Arial", 10, "bold")).grid(row=0, column=0, padx=2, pady=2)
        ttk.Label(header_frame, text="最小比例(%)", width=15, font=("Arial", 10, "bold")).grid(row=0, column=1, padx=2, pady=2)
        ttk.Label(header_frame, text="最大比例(%)", width=15, font=("Arial", 10, "bold")).grid(row=0, column=2, padx=2, pady=2)
        ttk.Label(header_frame, text="状态", width=15, font=("Arial", 10, "bold")).grid(row=0, column=3, padx=2, pady=2)

        # 创建滚动框架用于表格内容
        canvas = tk.Canvas(table_frame, height=400)
        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # 存储输入框变量
        constraint_vars = {}

        # 为每个渠道创建输入行
        for i, channel in enumerate(self.channels):
            row_frame = ttk.Frame(scrollable_frame)
            row_frame.pack(fill=tk.X, pady=1)

            # 渠道名称
            ttk.Label(row_frame, text=channel, width=20).grid(row=0, column=0, padx=2, sticky=tk.W)

            # 获取当前约束值
            if channel in self.config["channel_constraints"]:
                constraint = self.config["channel_constraints"][channel]
                min_val = constraint["min"] * 100
                max_val = constraint["max"] * 100
                status = "自定义"
            else:
                min_val = 2.0
                max_val = 60.0
                status = "默认"

            # 最小比例输入框
            min_var = tk.DoubleVar(value=min_val)
            min_entry = ttk.Entry(row_frame, textvariable=min_var, width=12)
            min_entry.grid(row=0, column=1, padx=2)

            # 最大比例输入框
            max_var = tk.DoubleVar(value=max_val)
            max_entry = ttk.Entry(row_frame, textvariable=max_var, width=12)
            max_entry.grid(row=0, column=2, padx=2)

            # 状态标签
            status_label = ttk.Label(row_frame, text=status, width=12)
            status_label.grid(row=0, column=3, padx=2)

            # 保存变量引用
            constraint_vars[channel] = {
                'min_var': min_var,
                'max_var': max_var,
                'status_label': status_label,
                'min_entry': min_entry,
                'max_entry': max_entry
            }

            # 绑定变化事件
            def on_change(channel=channel):
                min_val = constraint_vars[channel]['min_var'].get()
                max_val = constraint_vars[channel]['max_var'].get()

                # 验证输入
                if min_val < 0 or max_val > 100 or min_val > max_val:
                    constraint_vars[channel]['status_label'].config(text="❌ 无效", foreground="red")
                elif min_val == 2.0 and max_val == 60.0:
                    constraint_vars[channel]['status_label'].config(text="默认", foreground="black")
                else:
                    constraint_vars[channel]['status_label'].config(text="✅ 自定义", foreground="green")

            min_var.trace_add('write', lambda *args, ch=channel: on_change(ch))
            max_var.trace_add('write', lambda *args, ch=channel: on_change(ch))

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 批量操作函数
        def apply_to_all(min_val, max_val, description):
            """应用约束到所有渠道"""
            if messagebox.askyesno("确认", f"确定要将所有渠道约束设置为 {description} 吗？"):
                for channel in self.channels:
                    constraint_vars[channel]['min_var'].set(min_val)
                    constraint_vars[channel]['max_var'].set(max_val)
                messagebox.showinfo("成功", f"已应用 {description}")

        def reset_all_constraints():
            """重置所有约束为默认值"""
            apply_to_all(2.0, 60.0, "默认约束(2%-60%)")

        def save_all_constraints():
            """保存所有约束设置"""
            invalid_channels = []

            # 验证所有输入
            for channel in self.channels:
                min_val = constraint_vars[channel]['min_var'].get()
                max_val = constraint_vars[channel]['max_var'].get()

                if min_val < 0 or max_val > 100 or min_val > max_val:
                    invalid_channels.append(channel)

            if invalid_channels:
                messagebox.showerror("错误", f"以下渠道的约束值无效：\n{', '.join(invalid_channels)}")
                return

            # 保存有效的约束
            for channel in self.channels:
                min_val = constraint_vars[channel]['min_var'].get()
                max_val = constraint_vars[channel]['max_var'].get()

                if min_val == 2.0 and max_val == 60.0:
                    # 使用默认值，删除自定义约束
                    if channel in self.config["channel_constraints"]:
                        del self.config["channel_constraints"][channel]
                else:
                    # 保存自定义约束
                    self.config["channel_constraints"][channel] = {
                        "min": min_val / 100,
                        "max": max_val / 100
                    }

            messagebox.showinfo("成功", "约束设置已保存")
            constraint_window.destroy()

            # 刷新渠道信息显示
            self.load_channels()

        # 按钮区域
        button_frame = ttk.Frame(constraint_window)
        button_frame.pack(fill=tk.X, padx=10, pady=10)

        # 预设按钮
        preset_frame = ttk.LabelFrame(button_frame, text="快速设置", padding="5")
        preset_frame.pack(fill=tk.X, pady=5)

        ttk.Button(preset_frame, text="保守型 (2%-60%)",
                  command=lambda: apply_to_all(2.0, 60.0, "保守型约束")).pack(side=tk.LEFT, padx=5)
        ttk.Button(preset_frame, text="均衡型 (5%-40%)",
                  command=lambda: apply_to_all(5.0, 40.0, "均衡型约束")).pack(side=tk.LEFT, padx=5)
        ttk.Button(preset_frame, text="激进型 (1%-80%)",
                  command=lambda: apply_to_all(1.0, 80.0, "激进型约束")).pack(side=tk.LEFT, padx=5)

        # 操作按钮
        action_frame = ttk.Frame(button_frame)
        action_frame.pack(fill=tk.X, pady=5)

        ttk.Button(action_frame, text="💾 保存设置", command=save_all_constraints).pack(side=tk.LEFT, padx=5)
        ttk.Button(action_frame, text="🔄 重置为默认", command=reset_all_constraints).pack(side=tk.LEFT, padx=5)
        ttk.Button(action_frame, text="❌ 取消", command=constraint_window.destroy).pack(side=tk.RIGHT, padx=5)

    def save_config(self):
        """保存配置"""
        self.config.update({
            "excel_file": self.file_var.get(),
            "worksheet_name": self.worksheet_var.get(),
            "input_start_row": self.start_row_var.get(),
            "input_end_row": self.end_row_var.get(),
            "input_start_col": self.col_letter_to_num(self.start_col_var.get()),  # 转换为数字保存
            "input_end_col": self.col_letter_to_num(self.end_col_var.get()),      # 转换为数字保存
            "output_col": self.col_letter_to_num(self.output_col_var.get()),      # 转换为数字保存
            "output_start_row": self.output_start_row_var.get(),
            "output_end_row": self.output_end_row_var.get(),
            "channel_names_row": self.channel_row_var.get(),
            "total_budget": self.budget_var.get(),
            "optimization_schemes": self.schemes_var.get(),
            "max_iterations": self.iterations_var.get(),  # 添加迭代次数参数
            "baseline_allocation": self.baseline_var.get(),
            "output_directory": self.output_dir_var.get(),  # 添加输出目录
            "output_prefix": self.output_prefix_var.get(),   # 添加文件名前缀
            "kpi_type": self.kpi_type_var.get()  # 添加KPI类型
        })

        filename = filedialog.asksaveasfilename(
            title="保存配置文件",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(self.config, f, indent=2, ensure_ascii=False)
                messagebox.showinfo("成功", "配置已保存")
            except Exception as e:
                messagebox.showerror("错误", f"保存配置失败: {str(e)}")

    def load_config(self):
        """加载默认配置"""
        config_file = "media_optimizer_config.json"
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    saved_config = json.load(f)
                    self.config.update(saved_config)
                    self.update_ui_from_config()
            except:
                pass

    def load_config_file(self):
        """加载配置文件"""
        filename = filedialog.askopenfilename(
            title="加载配置文件",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
                    self.update_ui_from_config()
                messagebox.showinfo("成功", "配置已加载")
            except Exception as e:
                messagebox.showerror("错误", f"加载配置失败: {str(e)}")

    def update_ui_from_config(self):
        """从配置更新界面"""
        self.file_var.set(self.config.get("excel_file", ""))
        self.worksheet_var.set(self.config.get("worksheet_name", ""))  # 移除默认值
        self.start_row_var.set(self.config.get("input_start_row", 1))  # 移除默认值
        self.end_row_var.set(self.config.get("input_end_row", 1))      # 移除默认值
        self.start_col_var.set(self.col_num_to_letter(self.config.get("input_start_col", 1)))  # 转换为字母显示
        self.end_col_var.set(self.col_num_to_letter(self.config.get("input_end_col", 1)))      # 转换为字母显示
        self.output_col_var.set(self.col_num_to_letter(self.config.get("output_col", 1)))      # 转换为字母显示
        self.output_start_row_var.set(self.config.get("output_start_row", 1))  # 移除默认值
        self.output_end_row_var.set(self.config.get("output_end_row", 1))      # 移除默认值
        self.channel_row_var.set(self.config.get("channel_names_row", 1))
        self.budget_var.set(self.config.get("total_budget", 1000000))
        self.schemes_var.set(self.config.get("optimization_schemes", 5))
        self.iterations_var.set(self.config.get("max_iterations", 200))
        self.baseline_var.set(self.config.get("baseline_allocation", "equal"))
        self.kpi_type_var.set(self.config.get("kpi_type", "Non-BHT"))  # 添加KPI类型
        self.output_dir_var.set(self.config.get("output_directory", os.getcwd()))
        self.output_prefix_var.set(self.config.get("output_prefix", "媒体预算优化结果"))
        self.on_baseline_changed()  # 更新自定义基准按钮状态

    def start_optimization(self):
        """开始优化"""
        # 验证输入
        if not self.file_var.get() or not os.path.exists(self.file_var.get()):
            messagebox.showerror("错误", "请选择有效的Excel文件")
            return

        if not self.channels:
            messagebox.showerror("错误", "请先读取渠道信息")
            return

        # 验证自定义基准配比
        if self.baseline_var.get() == "custom":
            if not self.config["custom_baseline_ratios"]:
                messagebox.showerror("错误", "请先设置自定义基准配比")
                return

            total_ratio = sum(self.config["custom_baseline_ratios"].values())
            if abs(total_ratio - 100) > 0.1:
                messagebox.showerror("错误", f"自定义基准配比总和应为100%，当前为{total_ratio:.1f}%")
                return

        # 更新配置（确保使用最新的界面设置）
        self.update_config_from_ui()

        # 在新线程中运行优化
        self.progress['value'] = 0
        self.progress['maximum'] = 100
        self.status_var.set("正在优化...")
        self.progress_detail_var.set("准备开始...")

        # 更新按钮状态
        self.start_btn.config(state="disabled")
        self.pause_btn.config(state="normal")
        self.resume_btn.config(state="disabled")
        self.stop_btn.config(state="normal")

        optimization_thread = threading.Thread(target=self.run_optimization)
        optimization_thread.daemon = True
        optimization_thread.start()

    def pause_optimization(self):
        """暂停优化"""
        if self.optimizer_engine:
            self.optimizer_engine.pause_optimization()
            self.pause_btn.config(state="disabled")
            self.resume_btn.config(state="normal")

    def resume_optimization(self):
        """恢复优化"""
        if self.optimizer_engine:
            self.optimizer_engine.resume_optimization()
            self.pause_btn.config(state="normal")
            self.resume_btn.config(state="disabled")

    def terminate_optimization(self):
        """终止优化"""
        if self.optimizer_engine:
            self.optimizer_engine.terminate_optimization()
            self.reset_button_states()

    def reset_button_states(self):
        """重置按钮状态"""
        self.start_btn.config(state="normal")
        self.pause_btn.config(state="disabled")
        self.resume_btn.config(state="disabled")
        self.stop_btn.config(state="disabled")

    def update_config_from_ui(self):
        """从界面更新配置"""
        self.config.update({
            "excel_file": self.file_var.get(),
            "worksheet_name": self.worksheet_var.get(),
            "input_start_row": self.start_row_var.get(),
            "input_end_row": self.end_row_var.get(),
            "input_start_col": self.col_letter_to_num(self.start_col_var.get()),  # 转换为数字
            "input_end_col": self.col_letter_to_num(self.end_col_var.get()),      # 转换为数字
            "output_col": self.col_letter_to_num(self.output_col_var.get()),      # 转换为数字
            "output_start_row": self.output_start_row_var.get(),
            "output_end_row": self.output_end_row_var.get(),
            "channel_names_row": self.channel_row_var.get(),
            "total_budget": self.budget_var.get(),
            "optimization_schemes": self.schemes_var.get(),
            "max_iterations": self.iterations_var.get(),
            "baseline_allocation": self.baseline_var.get(),
            "kpi_type": self.kpi_type_var.get()  # 添加KPI类型
        })

    def update_progress(self, progress, status, detail=""):
        """更新进度显示"""
        self.progress['value'] = progress
        self.status_var.set(status)
        self.progress_detail_var.set(detail)

        # 更新监控状态
        self.update_monitor_status(status)

        # 更新实时监控信息
        if "基准KPI" in detail:
            try:
                kpi_value = detail.split("基准KPI: ")[1].split(",")[0]
                self.baseline_kpi_var.set(kpi_value)
                self.add_log(f"📈 基准KPI已计算: {kpi_value}")
            except:
                pass

        # 解析迭代进度
        if "迭代" in detail and "/" in detail:
            try:
                # 提取迭代信息，如 "迭代 50/200"
                import re
                match = re.search(r'(\d+)/(\d+)', detail)
                if match:
                    current, total = int(match.group(1)), int(match.group(2))
                    self.update_iteration_progress(current, total)
            except:
                pass

        # 解析方案生成信息
        if "生成" in detail and "方案" in detail:
            try:
                import re
                match = re.search(r'(\d+)\s*个', detail)
                if match:
                    count = int(match.group(1))
                    self.update_schemes_count(count)
            except:
                pass

    def add_log(self, message):
        """添加日志信息"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)  # 滚动到底部
        self.log_text.config(state=tk.DISABLED)

    def update_monitor_status(self, status, color="#000000"):
        """更新监控状态"""
        self.optimization_status_var.set(status)
        self.add_log(f"状态更新: {status}")

    def update_iteration_progress(self, current, total):
        """更新迭代进度"""
        self.iteration_var.set(f"{current}/{total}")
        if total > 0:
            progress_pct = (current / total) * 100
            self.add_log(f"迭代进度: {current}/{total} ({progress_pct:.1f}%)")

    def update_schemes_count(self, count):
        """更新生成方案数量"""
        self.schemes_count_var.set(f"{count} 个")
        self.add_log(f"已生成 {count} 个优化方案")

    def update_preview(self, content):
        """更新预览窗口内容"""
        self.preview_text.config(state=tk.NORMAL)
        self.preview_text.delete(1.0, tk.END)
        self.preview_text.insert(tk.END, content)
        self.preview_text.config(state=tk.DISABLED)
        self.preview_text.see(tk.END)  # 滚动到底部

    def run_optimization(self):
        """运行优化（在后台线程中）"""
        try:
            # 调用优化引擎
            from universal_optimizer_engine_v2 import UniversalOptimizerEngineV2

            # 创建进度回调函数
            def progress_callback(progress, status, detail=""):
                # 处理特殊进度值
                if progress == -1:  # 暂停/恢复/终止状态
                    self.root.after(0, self.update_progress, self.progress['value'], status, detail)
                else:
                    self.root.after(0, self.update_progress, progress, status, detail)

            # 创建并保存优化引擎实例
            self.optimizer_engine = UniversalOptimizerEngineV2(self.config, self.channels, progress_callback)
            results = self.optimizer_engine.optimize()

            # 在主线程中更新UI
            self.root.after(0, self.optimization_complete, results)

        except Exception as e:
            self.root.after(0, self.optimization_error, str(e))
        finally:
            # 清理引擎实例
            self.optimizer_engine = None

    def optimization_complete(self, results):
        """优化完成"""
        self.progress['value'] = 100
        self.status_var.set("优化完成")
        self.progress_detail_var.set("所有任务已完成")
        self.reset_button_states()

        # 更新实时监控信息
        if results and "results" in results and results["results"]:
            best_result = max(results["results"], key=lambda x: x.get("KPI", 0))
            baseline_kpi = results.get("baseline_kpi", 0)

            self.best_kpi_var.set(f"{best_result.get('KPI', 0):,.2f}")
            if baseline_kpi > 0:
                improvement = ((best_result.get('KPI', 0) - baseline_kpi) / baseline_kpi) * 100
                self.improvement_var.set(f"{improvement:.2f}%")

        # 生成结果预览
        preview_content = self.generate_results_preview(results)
        self.update_preview(preview_content)

        # 询问用户是否保存结果
        save_response = messagebox.askyesno(
            "优化完成",
            f"🎉 优化完成！\n\n"
            f"📊 生成了 {len(results.get('results', []))} 个优化方案\n"
            f"📈 最大提升幅度: {self.improvement_var.get()}\n\n"
            f"请在右侧预览窗口查看详细结果。\n\n"
            f"是否保存结果到Excel文件？"
        )

        if save_response:
            self.save_optimization_results(results)

        # 详细的结果文件确认
        if results and "output_file" in results:
            output_file = results["output_file"]

            # 检查文件是否真的存在
            if os.path.exists(output_file):
                file_size = os.path.getsize(output_file)
                file_size_mb = file_size / (1024 * 1024)

                # 获取文件的绝对路径
                abs_path = os.path.abspath(output_file)

                success_msg = (
                    f"🎉 优化完成！\n\n"
                    f"📊 生成方案数量: {len(results['results'])} 个\n"
                    f"📁 结果文件: {os.path.basename(output_file)}\n"
                    f"📂 保存位置: {os.path.dirname(abs_path)}\n"
                    f"📏 文件大小: {file_size_mb:.2f} MB\n\n"
                    f"✅ 文件已成功保存并验证"
                )

                # 询问是否打开文件所在目录
                response = messagebox.askyesno(
                    "优化成功",
                    success_msg + "\n\n是否打开文件所在目录？"
                )

                if response:
                    try:
                        # 打开文件所在目录并选中文件
                        import subprocess
                        subprocess.run(['explorer', '/select,', abs_path])
                    except Exception as e:
                        print(f"打开目录失败: {e}")
                        # 备用方案：只打开目录
                        try:
                            os.startfile(os.path.dirname(abs_path))
                        except Exception as e2:
                            print(f"打开目录失败: {e2}")
            else:
                # 文件不存在，显示错误信息
                error_msg = (
                    f"⚠️ 优化完成，但结果文件未找到！\n\n"
                    f"📊 生成方案数量: {len(results['results'])} 个\n"
                    f"❌ 预期文件位置: {output_file}\n"
                    f"📂 当前工作目录: {os.getcwd()}\n\n"
                    f"可能原因:\n"
                    f"• 文件保存权限不足\n"
                    f"• 磁盘空间不足\n"
                    f"• 输出目录不存在\n"
                    f"• 文件名包含非法字符"
                )
                messagebox.showerror("文件保存失败", error_msg)
        else:
            # 没有输出文件信息
            if results and "results" in results:
                warning_msg = (
                    f"⚠️ 优化完成，但未生成结果文件！\n\n"
                    f"📊 生成方案数量: {len(results['results'])} 个\n"
                    f"📂 当前工作目录: {os.getcwd()}\n\n"
                    f"建议检查:\n"
                    f"• 输出目录设置是否正确\n"
                    f"• 是否有文件写入权限\n"
                    f"• 磁盘空间是否充足"
                )
                messagebox.showwarning("结果文件缺失", warning_msg)
            else:
                messagebox.showinfo("优化完成", "优化完成，但未获取到结果信息")

    def generate_results_preview(self, results):
        """生成结果预览内容"""
        if not results or "results" not in results:
            return "❌ 未获取到优化结果"

        preview = "📊 优化结果摘要\n"
        preview += "=" * 50 + "\n\n"

        baseline_kpi = results.get("baseline_kpi", 0)
        optimization_results = results["results"]

        preview += f"📈 基准KPI: {baseline_kpi:,.2f}\n"
        preview += f"🎯 生成方案数: {len(optimization_results)} 个\n"
        preview += f"⏱️ KPI类型: {self.config.get('kpi_type', 'Non-BHT')}\n\n"

        # 显示前5个最优方案
        sorted_results = sorted(optimization_results, key=lambda x: x.get("KPI", 0), reverse=True)
        preview += "🏆 最优方案排行榜:\n"
        preview += "-" * 40 + "\n"

        for i, result in enumerate(sorted_results[:5], 1):
            kpi = result.get("KPI", 0)
            improvement = ((kpi - baseline_kpi) / baseline_kpi * 100) if baseline_kpi > 0 else 0
            preview += f"{i}. {result.get('方案名称', 'Unknown')}\n"
            preview += f"   KPI: {kpi:,.2f} (+{improvement:.2f}%)\n\n"

        # 显示最优方案的预算分配
        if sorted_results:
            best_result = sorted_results[0]
            preview += "💰 最优方案预算分配:\n"
            preview += "-" * 40 + "\n"
            budget_allocation = best_result.get("预算分配", {})
            total_budget = sum(budget_allocation.values()) if budget_allocation else 0

            for channel, budget in budget_allocation.items():
                percentage = (budget / total_budget * 100) if total_budget > 0 else 0
                preview += f"{channel}: ¥{budget:,.0f} ({percentage:.1f}%)\n"

        preview += "\n" + "=" * 50 + "\n"
        preview += "✅ 优化完成！可选择保存详细结果到Excel文件。"

        return preview

    def save_optimization_results(self, results):
        """保存优化结果"""
        try:
            # 调用优化引擎的导出功能
            if hasattr(self, 'optimizer_engine') and self.optimizer_engine:
                output_file = self.optimizer_engine.export_results(
                    results["results"],
                    results.get("baseline_kpi", 0),
                    results.get("channel_performance", [])
                )
            else:
                # 如果引擎实例不存在，创建临时实例进行导出
                from universal_optimizer_engine_v2 import UniversalOptimizerEngineV2
                temp_engine = UniversalOptimizerEngineV2(self.config, self.channels, None)
                output_file = temp_engine.export_results(
                    results["results"],
                    results.get("baseline_kpi", 0),
                    results.get("channel_performance", [])
                )

            if output_file and os.path.exists(output_file):
                messagebox.showinfo("保存成功", f"结果已保存到:\n{output_file}")

                # 询问是否打开文件
                if messagebox.askyesno("打开文件", "是否打开结果文件？"):
                    try:
                        os.startfile(output_file)
                    except:
                        import subprocess
                        subprocess.run(['explorer', '/select,', os.path.abspath(output_file)])
            else:
                messagebox.showerror("保存失败", "无法保存结果文件")

        except Exception as e:
            messagebox.showerror("保存失败", f"保存结果时出错: {str(e)}")

    def optimization_error(self, error_msg):
        """优化出错"""
        self.progress['value'] = 0
        self.status_var.set("优化失败")
        self.progress_detail_var.set("发生错误")
        self.reset_button_states()

        # 更新预览窗口显示错误信息
        error_preview = f"❌ 优化失败\n\n错误信息:\n{error_msg}\n\n请检查配置并重试。"
        self.update_preview(error_preview)

        messagebox.showerror("错误", f"优化失败: {error_msg}")


if __name__ == "__main__":
    root = tk.Tk()
    app = UniversalMediaOptimizerV2(root)
    root.mainloop()
