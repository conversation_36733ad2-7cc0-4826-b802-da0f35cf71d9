@echo off
chcp 65001 >nul
title DEBUG - 媒体预算优化工具 v3.0 启动器

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    DEBUG 模式启动器                         ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🔍 开始诊断启动问题...
echo.

REM 显示当前目录
echo 📁 当前目录: %CD%
echo.

REM 检查文件是否存在
echo 🔍 检查必要文件...
if exist "universal_media_optimizer_v3.py" (
    echo ✅ 找到主程序文件: universal_media_optimizer_v3.py
) else (
    echo ❌ 缺少主程序文件: universal_media_optimizer_v3.py
    echo.
    echo 📋 当前目录文件列表:
    dir /b
    echo.
    pause
    exit /b 1
)

if exist "universal_optimizer_engine_v2.py" (
    echo ✅ 找到引擎文件: universal_optimizer_engine_v2.py
) else (
    echo ❌ 缺少引擎文件: universal_optimizer_engine_v2.py
)

echo.

REM 检查Python环境
echo 🐍 检查Python环境...
python --version 2>nul
if %errorlevel% neq 0 (
    echo ❌ Python未安装或未添加到PATH
    echo.
    echo 💡 解决方案:
    echo 1. 下载Python: https://www.python.org/downloads/
    echo 2. 安装时勾选 "Add Python to PATH"
    echo 3. 重启命令行窗口
    echo.
    echo 🔄 或者尝试使用 py 命令...
    py --version 2>nul
    if %errorlevel% neq 0 (
        echo ❌ py 命令也不可用
        echo.
        pause
        exit /b 1
    ) else (
        echo ✅ 找到 py 命令，将使用 py 替代 python
        set PYTHON_CMD=py
    )
) else (
    echo ✅ Python环境正常
    set PYTHON_CMD=python
)

echo.

REM 检查Python模块
echo 📦 检查Python模块...
%PYTHON_CMD% -c "import sys; print('Python版本:', sys.version)" 2>nul
if %errorlevel% neq 0 (
    echo ❌ Python基本功能异常
    pause
    exit /b 1
)

echo.
echo 🔍 检查tkinter模块...
%PYTHON_CMD% -c "import tkinter; print('✅ tkinter可用')" 2>nul
if %errorlevel% neq 0 (
    echo ❌ tkinter模块不可用
    echo 💡 这可能是Python安装不完整导致的
    pause
    exit /b 1
)

echo.
echo 🔍 检查win32com模块...
%PYTHON_CMD% -c "import win32com.client; print('✅ win32com可用')" 2>nul
if %errorlevel% neq 0 (
    echo ⚠️ win32com模块不可用，正在尝试安装...
    %PYTHON_CMD% -m pip install pywin32
    if %errorlevel% neq 0 (
        echo ❌ pywin32安装失败
        echo 💡 请手动运行: pip install pywin32
        pause
        exit /b 1
    )
    echo ✅ pywin32安装成功
)

echo.
echo 🚀 所有检查通过，尝试启动程序...
echo.
echo ================================================
echo 以下是程序的详细输出信息:
echo ================================================
echo.

REM 启动程序并显示详细错误
%PYTHON_CMD% universal_media_optimizer_v3.py

echo.
echo ================================================
echo 程序执行完毕，错误代码: %errorlevel%
echo ================================================
echo.

if %errorlevel% neq 0 (
    echo ❌ 程序执行失败
    echo.
    echo 🔍 可能的原因:
    echo 1. Python代码中有语法错误
    echo 2. 缺少某些依赖模块
    echo 3. 文件权限问题
    echo 4. 系统兼容性问题
    echo.
    echo 💡 建议:
    echo 1. 查看上方的详细错误信息
    echo 2. 尝试以管理员身份运行
    echo 3. 使用备用的exe文件
    echo.
) else (
    echo ✅ 程序正常退出
)

echo.
echo 🔍 DEBUG信息收集完毕
pause
