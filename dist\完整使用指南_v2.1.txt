媒体预算优化工具 v2.1 - 完整使用指南
=====================================

🎉 界面美化版 - 完整解决方案

📁 文件说明
-----------
• 媒体预算优化工具_v2.1_美化版.exe - 🆕 最新推荐版本
• 媒体预算优化工具_v2.1_修复版.exe - 修复版本
• Gatorade simulation tool_cal.xlsx - 示例Excel文件
• 完整使用指南_v2.1.txt - 本使用指南
• 界面美化说明_v2.1.txt - 界面美化详细说明
• 修复说明_v2.1.txt - 启动问题修复说明
• README_最终版本.txt - 完整技术说明

🚀 快速开始（5分钟上手）
---------------------

**步骤1：启动程序**
1. 双击"媒体预算优化工具_v2.1_美化版.exe"
2. 等待30秒-2分钟程序启动（首次启动较慢）
3. 看到美化后的界面，布局清晰不重叠

**步骤2：配置Excel文件**
1. 在"1. Excel文件设置"区域点击"浏览"
2. 选择您的Excel文件（建议先用示例文件测试）
3. 确认工作表名称（通常为"calculation"）

**步骤3：设置输出位置**
1. 在"4. 输出设置"区域点击"📁 浏览"
2. 选择您希望保存结果的目录（如桌面、文档等）
3. 修改"文件名前缀"（如：项目A结果、月度报告等）

**步骤4：配置优化参数**
1. 在"3. 预算和优化设置"区域设置：
   - 总预算：您的总预算金额
   - 优化方案数量：建议3-6个
   - 迭代次数：快速测试50-100次，正式优化200-500次

**步骤5：开始优化**
1. 点击"📊 读取渠道"按钮加载渠道信息
2. 点击"🚀 开始优化"按钮
3. 在"7. 优化进度"区域查看实时进度
4. 等待优化完成

**步骤6：查看结果**
1. 优化完成后会显示详细确认信息
2. 点击"是"自动打开文件所在目录
3. 结果文件会被自动选中，便于查看

🎯 界面布局说明
--------------

**美化后的界面结构**：
```
┌─ 1. Excel文件设置 ─────────────────┐
│ 文件路径、工作表名称等              │
└────────────────────────────────────┘

┌─ 2. 数据区域设置 ──────────────────┐
│ 输入区域、输出区域、渠道名称行      │
└────────────────────────────────────┘

┌─ 3. 预算和优化设置 ────────────────┐
│ 总预算: [1000000] 元               │
│ 优化方案数量: [5] 个  迭代次数: [200] 次│
│ 基准方案: [equal] [设置自定义配比]   │
└────────────────────────────────────┘

┌─ 4. 输出设置 ──────────────────────┐
│ 输出目录: [C:\Users\<USER>