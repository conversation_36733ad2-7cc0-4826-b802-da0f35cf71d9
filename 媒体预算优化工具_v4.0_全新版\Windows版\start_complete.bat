@echo off
title Media Budget Optimizer v4.0 Complete

echo.
echo ========================================
echo    Media Budget Optimizer v4.0 Complete
echo    All Features Included
echo ========================================
echo.
echo Starting complete version...
echo.

REM Check Python environment
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python not found!
    echo.
    echo Solution:
    echo 1. Install Python 3.7+
    echo 2. Check "Add Python to PATH" during installation
    echo 3. Restart command prompt
    echo.
    pause
    exit /b 1
)

echo Python environment OK
echo.

REM Check dependencies
echo Checking dependencies...
python -c "import win32com.client" >nul 2>&1
if %errorlevel% neq 0 (
    echo Installing pywin32...
    python -m pip install pywin32
    if %errorlevel% neq 0 (
        echo Installation failed!
        echo Please run manually: pip install pywin32
        pause
        exit /b 1
    )
    echo pywin32 installed successfully
)

echo Dependencies OK
echo.

REM Start complete version
echo Starting Media Budget Optimizer v4.0 Complete...
echo Features: Responsive Layout, Channel Classification, Excel Integration
echo.

python media_optimizer_v4_complete.py

REM Cleanup after exit
echo.
echo Program exited
echo Cleaning up...

REM Kill Python processes
taskkill /f /im python.exe >nul 2>&1

echo.
echo Program closed successfully
echo Thank you for using Media Budget Optimizer v4.0 Complete!

timeout /t 3 /nobreak >nul
