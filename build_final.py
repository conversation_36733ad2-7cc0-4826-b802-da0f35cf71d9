#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终打包脚本 - 生成真正的v3.0可执行文件
"""

import subprocess
import sys
import os
import shutil
from datetime import datetime

def main():
    print("🚀 开始生成v3.0最终版本...")
    
    # 清理旧文件
    if os.path.exists("dist"):
        print("🧹 清理旧的dist目录...")
        shutil.rmtree("dist")
    
    if os.path.exists("build"):
        print("🧹 清理旧的build目录...")
        shutil.rmtree("build")
    
    # 检查源文件
    if not os.path.exists("universal_media_optimizer_v2.py"):
        print("❌ 找不到v3.0源文件")
        return False
    
    print("✅ 找到v3.0源文件")
    
    # 打包Windows版本
    print("🔨 开始打包Windows版本...")
    try:
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--onefile",
            "--windowed",
            "--name=MediaBudgetOptimizer_v3.0",
            "--add-data=universal_optimizer_engine_v2.py;.",
            "universal_media_optimizer_v2.py"
        ]
        
        print("执行命令:", " ".join(cmd))
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            print("✅ Windows版本打包成功!")
            return True
        else:
            print("❌ Windows版本打包失败")
            print("STDOUT:", result.stdout)
            print("STDERR:", result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ 打包超时")
        return False
    except Exception as e:
        print(f"❌ 打包出错: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 打包完成!")
        if os.path.exists("dist"):
            print("📁 输出文件:")
            for file in os.listdir("dist"):
                print(f"  - {file}")
    else:
        print("\n❌ 打包失败")
    
    input("按回车键退出...")
