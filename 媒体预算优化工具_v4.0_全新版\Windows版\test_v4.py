#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试v4.0程序是否能正常启动
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys

def test_basic():
    """测试基本tkinter功能"""
    try:
        root = tk.Tk()
        root.title("测试窗口")
        root.geometry("400x300")
        
        label = tk.Label(root, text="如果你看到这个窗口，说明tkinter工作正常")
        label.pack(pady=50)
        
        button = tk.Button(root, text="关闭", command=root.destroy)
        button.pack(pady=20)
        
        print("基本测试窗口已创建")
        root.mainloop()
        print("基本测试完成")
        
    except Exception as e:
        print(f"基本测试失败: {e}")
        return False
    return True

def test_imports():
    """测试导入"""
    try:
        print("测试导入...")
        import tkinter as tk
        print("✅ tkinter导入成功")
        
        from tkinter import ttk, filedialog, messagebox, scrolledtext
        print("✅ tkinter子模块导入成功")
        
        import json
        print("✅ json导入成功")
        
        import os
        print("✅ os导入成功")
        
        import threading
        print("✅ threading导入成功")
        
        import time
        print("✅ time导入成功")
        
        from datetime import datetime
        print("✅ datetime导入成功")
        
        import re
        print("✅ re导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_v4_import():
    """测试v4程序导入"""
    try:
        print("测试v4程序导入...")
        
        # 尝试导入v4程序
        import universal_media_optimizer_v4
        print("✅ v4程序导入成功")
        
        # 尝试创建实例
        app = universal_media_optimizer_v4.MediaOptimizerV4()
        print("✅ v4程序实例创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ v4程序测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("开始诊断v4.0程序...")
    print("=" * 50)
    
    # 测试1: 基本导入
    if not test_imports():
        print("导入测试失败，程序无法继续")
        return
    
    print("\n" + "=" * 50)
    
    # 测试2: v4程序导入
    if not test_v4_import():
        print("v4程序导入失败")
        return
    
    print("\n" + "=" * 50)
    
    # 测试3: 基本tkinter功能
    print("测试基本tkinter功能...")
    if test_basic():
        print("✅ 所有测试通过！")
    else:
        print("❌ 基本功能测试失败")

if __name__ == "__main__":
    main()
