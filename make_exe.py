#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
强制生成exe文件
"""

import os
import sys
import subprocess
import time

def main():
    print("🚀 强制生成v3.0 exe文件...")
    
    # 确保PyInstaller可用
    try:
        import PyInstaller
        print("✅ PyInstaller已准备就绪")
    except ImportError:
        print("📦 安装PyInstaller...")
        subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"])
        import PyInstaller
    
    # 检查源文件
    if not os.path.exists("universal_media_optimizer_v2.py"):
        print("❌ 找不到v3.0源文件")
        return
    
    print("✅ 找到v3.0源文件")
    
    # 简单直接的打包命令
    cmd = [
        "pyinstaller",
        "--onefile",
        "--windowed",
        "--name=媒体预算优化工具v3.0",
        "universal_media_optimizer_v2.py"
    ]
    
    print("🔨 开始打包...")
    print("命令:", " ".join(cmd))
    
    try:
        # 直接运行命令
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        print("返回码:", result.returncode)
        if result.stdout:
            print("输出:", result.stdout)
        if result.stderr:
            print("错误:", result.stderr)
        
        # 检查结果
        exe_path = os.path.join("dist", "媒体预算优化工具v3.0.exe")
        if os.path.exists(exe_path):
            print("🎉 成功生成exe文件!")
            print(f"文件位置: {exe_path}")
            print(f"文件大小: {os.path.getsize(exe_path) / 1024 / 1024:.1f} MB")
            return True
        else:
            print("❌ 未找到生成的exe文件")
            return False
            
    except Exception as e:
        print(f"❌ 打包失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n尝试备用方法...")
        # 备用方法：使用python -m PyInstaller
        try:
            cmd2 = [
                sys.executable, "-m", "PyInstaller",
                "--onefile", "--windowed",
                "--name=媒体预算优化工具v3.0",
                "universal_media_optimizer_v2.py"
            ]
            print("备用命令:", " ".join(cmd2))
            subprocess.run(cmd2)
            
            exe_path = os.path.join("dist", "媒体预算优化工具v3.0.exe")
            if os.path.exists(exe_path):
                print("🎉 备用方法成功!")
            else:
                print("❌ 备用方法也失败了")
        except Exception as e:
            print(f"❌ 备用方法失败: {e}")
    
    input("按回车键退出...")
