@echo off
chcp 65001 >nul
title 媒体预算优化工具 v3.0 启动器

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                媒体预算优化工具 v3.0 增强版                 ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🆕 v3.0 新功能：
echo   • KPI类型选择（BHT/Non-BHT）
echo   • 16:9屏幕优化布局
echo   • 实时监控仪表板  
echo   • 滑块配比设置
echo   • 美化Excel输出
echo   • 列字母输入
echo   • 结果预览功能
echo.
echo 🚀 正在启动...
echo.

REM 检查Python是否可用
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未检测到Python环境！
    echo.
    echo 💡 解决方案：
    echo 1. 安装Python 3.7或更高版本
    echo 2. 确保Python已添加到系统PATH
    echo 3. 重新打开命令行窗口
    echo.
    echo 📥 Python下载地址：https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

REM 启动程序
python 启动器.py

if %errorlevel% neq 0 (
    echo.
    echo ❌ 程序启动失败！
    echo.
    echo 💡 可能的解决方案：
    echo 1. 运行：pip install pywin32
    echo 2. 确保所有文件完整
    echo 3. 以管理员身份运行
    echo.
    pause
)

echo.
echo ✅ 程序已退出
pause
