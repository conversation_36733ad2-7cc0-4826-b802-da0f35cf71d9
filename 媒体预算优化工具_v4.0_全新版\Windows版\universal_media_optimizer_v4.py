#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
媒体预算优化工具 v4.0 - 全新版
包含所有改进功能：界面美化、自适应布局、基准比例计算、实时监控等
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import json
import os
import threading
import time
from datetime import datetime
import re
import signal
import sys

class MediaOptimizerV4:
    def __init__(self):
        try:
            print("初始化媒体预算优化工具 v4.0...")
            self.root = tk.Tk()
            print("✅ 创建主窗口成功")

            self.setup_window()
            print("✅ 窗口设置完成")

            self.setup_styles()
            print("✅ 样式设置完成")

            self.setup_variables()
            print("✅ 变量设置完成")

            self.create_widgets()
            print("✅ 界面组件创建完成")

            self.setup_layout()
            print("✅ 布局设置完成")

            self.setup_bindings()
            print("✅ 事件绑定完成")

            # 数据存储
            self.channels_data = []
            self.channel_types = {}
            self.optimization_running = False
            self.current_best_kpi = 0
            self.baseline_kpi = 0

            print("✅ 程序初始化完成！")

        except Exception as e:
            print(f"❌ 初始化失败: {e}")
            import traceback
            traceback.print_exc()
            raise
        
    def setup_window(self):
        """设置主窗口"""
        try:
            self.root.title("媒体预算优化工具 v4.0 - 全新版")
            self.root.geometry("1600x1000")
            self.root.minsize(1400, 900)

            # 设置窗口图标和样式
            self.root.configure(bg='#f0f0f0')

            # 窗口居中
            self.center_window()

            # 设置关闭事件
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        except Exception as e:
            print(f"❌ 窗口设置失败: {e}")
            raise
        
    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
        
    def setup_styles(self):
        """设置美化样式"""
        try:
            self.style = ttk.Style()
            self.style.theme_use('clam')

            # 定义颜色主题
            self.colors = {
                'primary': '#2E86AB',      # 主色调 - 蓝色
                'secondary': '#A23B72',    # 次色调 - 紫色
                'accent': '#F18F01',       # 强调色 - 橙色
                'success': '#C73E1D',      # 成功色 - 红色
                'background': '#F5F5F5',   # 背景色
                'surface': '#FFFFFF',      # 表面色
                'text': '#2C3E50',         # 文本色
                'text_light': '#7F8C8D',   # 浅文本色
                'border': '#BDC3C7'        # 边框色
            }

            # 配置样式
            self.configure_styles()

        except Exception as e:
            print(f"❌ 样式设置失败: {e}")
            # 使用默认颜色
            self.colors = {
                'primary': '#0078d4',
                'secondary': '#6264a7',
                'accent': '#ff8c00',
                'success': '#107c10',
                'background': '#f3f2f1',
                'surface': '#ffffff',
                'text': '#323130',
                'text_light': '#605e5c',
                'border': '#d2d0ce'
            }
        
    def configure_styles(self):
        """配置ttk样式"""
        # 主框架样式
        self.style.configure('Main.TFrame', 
                           background=self.colors['background'],
                           relief='flat')
        
        # 卡片样式框架
        self.style.configure('Card.TFrame',
                           background=self.colors['surface'],
                           relief='solid',
                           borderwidth=1)
        
        # 标题标签样式
        self.style.configure('Title.TLabel',
                           background=self.colors['surface'],
                           foreground=self.colors['primary'],
                           font=('Microsoft YaHei UI', 14, 'bold'))
        
        # 子标题样式
        self.style.configure('Subtitle.TLabel',
                           background=self.colors['surface'],
                           foreground=self.colors['text'],
                           font=('Microsoft YaHei UI', 10, 'bold'))
        
        # 普通标签样式
        self.style.configure('Normal.TLabel',
                           background=self.colors['surface'],
                           foreground=self.colors['text'],
                           font=('Microsoft YaHei UI', 9))
        
        # 按钮样式
        self.style.configure('Primary.TButton',
                           background=self.colors['primary'],
                           foreground='white',
                           font=('Microsoft YaHei UI', 10, 'bold'),
                           padding=(20, 10))
        
        self.style.configure('Secondary.TButton',
                           background=self.colors['secondary'],
                           foreground='white',
                           font=('Microsoft YaHei UI', 9),
                           padding=(15, 8))
        
        self.style.configure('Accent.TButton',
                           background=self.colors['accent'],
                           foreground='white',
                           font=('Microsoft YaHei UI', 9),
                           padding=(15, 8))
        
        # 输入框样式
        self.style.configure('Modern.TEntry',
                           fieldbackground='white',
                           borderwidth=2,
                           relief='solid',
                           font=('Microsoft YaHei UI', 9))
        
        # 下拉框样式
        self.style.configure('Modern.TCombobox',
                           fieldbackground='white',
                           borderwidth=2,
                           relief='solid',
                           font=('Microsoft YaHei UI', 9))
        
    def setup_variables(self):
        """设置变量"""
        # 文件路径
        self.file_path_var = tk.StringVar()
        self.sheet_name_var = tk.StringVar()
        
        # 数据范围
        self.data_start_row_var = tk.StringVar()
        self.data_end_row_var = tk.StringVar()
        self.data_start_col_var = tk.StringVar()
        self.data_end_col_var = tk.StringVar()
        self.channel_row_var = tk.StringVar()
        
        # 基准比例设置
        self.baseline_method_var = tk.StringVar(value="manual")  # manual, data_table, average
        self.baseline_start_row_var = tk.StringVar()
        self.baseline_end_row_var = tk.StringVar()
        self.baseline_start_col_var = tk.StringVar()
        self.baseline_end_col_var = tk.StringVar()
        
        # 优化参数
        self.total_budget_var = tk.StringVar()
        self.num_solutions_var = tk.StringVar()
        self.iterations_var = tk.StringVar()
        self.kpi_type_var = tk.StringVar(value="Non-BHT")
        
        # 实时监控
        self.baseline_kpi_var = tk.StringVar(value="0")
        self.current_best_kpi_var = tk.StringVar(value="0")
        self.improvement_var = tk.StringVar(value="0%")
        self.progress_var = tk.StringVar(value="0%")
        self.status_var = tk.StringVar(value="就绪")
        
    def create_widgets(self):
        """创建所有界面组件"""
        try:
            # 主容器
            self.main_container = ttk.Frame(self.root, style='Main.TFrame')
            self.main_container.pack(fill='both', expand=True, padx=10, pady=10)

            # 创建左右分栏
            self.create_left_panel()
            self.create_right_panel()

        except Exception as e:
            print(f"❌ 组件创建失败: {e}")
            # 创建简化界面
            self.create_fallback_interface()
        
    def create_left_panel(self):
        """创建左侧配置面板"""
        # 左侧滚动框架
        self.left_canvas = tk.Canvas(self.main_container, bg=self.colors['background'])
        self.left_scrollbar = ttk.Scrollbar(self.main_container, orient="vertical", command=self.left_canvas.yview)
        self.left_scrollable_frame = ttk.Frame(self.left_canvas, style='Main.TFrame')
        
        self.left_scrollable_frame.bind(
            "<Configure>",
            lambda e: self.left_canvas.configure(scrollregion=self.left_canvas.bbox("all"))
        )
        
        self.left_canvas.create_window((0, 0), window=self.left_scrollable_frame, anchor="nw")
        self.left_canvas.configure(yscrollcommand=self.left_scrollbar.set)
        
        # 创建配置卡片
        self.create_file_config_card()
        self.create_data_range_card()
        self.create_baseline_config_card()
        self.create_optimization_config_card()
        self.create_channel_constraints_card()
        
    def create_file_config_card(self):
        """创建文件配置卡片"""
        card = ttk.Frame(self.left_scrollable_frame, style='Card.TFrame')
        card.pack(fill='x', padx=5, pady=5)
        
        # 卡片标题
        title_frame = ttk.Frame(card, style='Card.TFrame')
        title_frame.pack(fill='x', padx=15, pady=(15, 10))
        
        ttk.Label(title_frame, text="📁 文件配置", style='Title.TLabel').pack(anchor='w')
        
        # 文件选择
        file_frame = ttk.Frame(card, style='Card.TFrame')
        file_frame.pack(fill='x', padx=15, pady=5)
        
        ttk.Label(file_frame, text="Excel文件:", style='Normal.TLabel').pack(anchor='w')
        
        file_input_frame = ttk.Frame(file_frame, style='Card.TFrame')
        file_input_frame.pack(fill='x', pady=(5, 0))
        
        self.file_entry = ttk.Entry(file_input_frame, textvariable=self.file_path_var, 
                                   style='Modern.TEntry', state='readonly')
        self.file_entry.pack(side='left', fill='x', expand=True, padx=(0, 10))
        
        ttk.Button(file_input_frame, text="浏览", style='Secondary.TButton',
                  command=self.browse_file).pack(side='right')
        
        # 工作表选择
        sheet_frame = ttk.Frame(card, style='Card.TFrame')
        sheet_frame.pack(fill='x', padx=15, pady=5)
        
        ttk.Label(sheet_frame, text="工作表:", style='Normal.TLabel').pack(anchor='w')
        self.sheet_combo = ttk.Combobox(sheet_frame, textvariable=self.sheet_name_var,
                                       style='Modern.TCombobox', state='readonly')
        self.sheet_combo.pack(fill='x', pady=(5, 15))
        
    def create_data_range_card(self):
        """创建数据范围配置卡片"""
        card = ttk.Frame(self.left_scrollable_frame, style='Card.TFrame')
        card.pack(fill='x', padx=5, pady=5)
        
        # 卡片标题
        title_frame = ttk.Frame(card, style='Card.TFrame')
        title_frame.pack(fill='x', padx=15, pady=(15, 10))
        
        ttk.Label(title_frame, text="📊 数据范围", style='Title.TLabel').pack(anchor='w')
        
        # 数据范围输入
        range_frame = ttk.Frame(card, style='Card.TFrame')
        range_frame.pack(fill='x', padx=15, pady=5)
        
        # 行范围
        row_frame = ttk.Frame(range_frame, style='Card.TFrame')
        row_frame.pack(fill='x', pady=5)
        
        ttk.Label(row_frame, text="数据行范围:", style='Normal.TLabel').pack(anchor='w')
        
        row_input_frame = ttk.Frame(row_frame, style='Card.TFrame')
        row_input_frame.pack(fill='x', pady=(5, 0))
        
        ttk.Entry(row_input_frame, textvariable=self.data_start_row_var, 
                 style='Modern.TEntry', width=8).pack(side='left', padx=(0, 5))
        ttk.Label(row_input_frame, text="到", style='Normal.TLabel').pack(side='left', padx=5)
        ttk.Entry(row_input_frame, textvariable=self.data_end_row_var, 
                 style='Modern.TEntry', width=8).pack(side='left', padx=(5, 0))
        
        # 列范围
        col_frame = ttk.Frame(range_frame, style='Card.TFrame')
        col_frame.pack(fill='x', pady=5)
        
        ttk.Label(col_frame, text="数据列范围:", style='Normal.TLabel').pack(anchor='w')
        
        col_input_frame = ttk.Frame(col_frame, style='Card.TFrame')
        col_input_frame.pack(fill='x', pady=(5, 0))
        
        ttk.Entry(col_input_frame, textvariable=self.data_start_col_var, 
                 style='Modern.TEntry', width=8).pack(side='left', padx=(0, 5))
        ttk.Label(col_input_frame, text="到", style='Normal.TLabel').pack(side='left', padx=5)
        ttk.Entry(col_input_frame, textvariable=self.data_end_col_var, 
                 style='Modern.TEntry', width=8).pack(side='left', padx=(5, 0))
        
        # 渠道名称行
        channel_frame = ttk.Frame(range_frame, style='Card.TFrame')
        channel_frame.pack(fill='x', pady=(5, 15))
        
        ttk.Label(channel_frame, text="渠道名称行:", style='Normal.TLabel').pack(anchor='w')
        ttk.Entry(channel_frame, textvariable=self.channel_row_var, 
                 style='Modern.TEntry', width=8).pack(anchor='w', pady=(5, 0))
        
    def create_baseline_config_card(self):
        """创建基准比例配置卡片"""
        card = ttk.Frame(self.left_scrollable_frame, style='Card.TFrame')
        card.pack(fill='x', padx=5, pady=5)
        
        # 卡片标题
        title_frame = ttk.Frame(card, style='Card.TFrame')
        title_frame.pack(fill='x', padx=15, pady=(15, 10))
        
        ttk.Label(title_frame, text="⚖️ 基准比例设置", style='Title.TLabel').pack(anchor='w')
        
        # 基准比例方法选择
        method_frame = ttk.Frame(card, style='Card.TFrame')
        method_frame.pack(fill='x', padx=15, pady=5)
        
        ttk.Label(method_frame, text="计算方式:", style='Normal.TLabel').pack(anchor='w')
        
        method_radio_frame = ttk.Frame(method_frame, style='Card.TFrame')
        method_radio_frame.pack(fill='x', pady=(5, 0))
        
        ttk.Radiobutton(method_radio_frame, text="手动设置", variable=self.baseline_method_var,
                       value="manual", command=self.on_baseline_method_change).pack(anchor='w')
        ttk.Radiobutton(method_radio_frame, text="数据表计算", variable=self.baseline_method_var,
                       value="data_table", command=self.on_baseline_method_change).pack(anchor='w')
        ttk.Radiobutton(method_radio_frame, text="平均分配", variable=self.baseline_method_var,
                       value="average", command=self.on_baseline_method_change).pack(anchor='w')
        
        # 数据表计算范围（默认隐藏）
        self.baseline_range_frame = ttk.Frame(card, style='Card.TFrame')
        self.baseline_range_frame.pack(fill='x', padx=15, pady=5)
        
        ttk.Label(self.baseline_range_frame, text="基准数据范围:", style='Subtitle.TLabel').pack(anchor='w')
        
        # 基准行范围
        baseline_row_frame = ttk.Frame(self.baseline_range_frame, style='Card.TFrame')
        baseline_row_frame.pack(fill='x', pady=5)
        
        ttk.Label(baseline_row_frame, text="行范围:", style='Normal.TLabel').pack(anchor='w')
        
        baseline_row_input_frame = ttk.Frame(baseline_row_frame, style='Card.TFrame')
        baseline_row_input_frame.pack(fill='x', pady=(5, 0))
        
        ttk.Entry(baseline_row_input_frame, textvariable=self.baseline_start_row_var, 
                 style='Modern.TEntry', width=8).pack(side='left', padx=(0, 5))
        ttk.Label(baseline_row_input_frame, text="到", style='Normal.TLabel').pack(side='left', padx=5)
        ttk.Entry(baseline_row_input_frame, textvariable=self.baseline_end_row_var, 
                 style='Modern.TEntry', width=8).pack(side='left', padx=(5, 0))
        
        # 基准列范围
        baseline_col_frame = ttk.Frame(self.baseline_range_frame, style='Card.TFrame')
        baseline_col_frame.pack(fill='x', pady=(5, 15))
        
        ttk.Label(baseline_col_frame, text="列范围:", style='Normal.TLabel').pack(anchor='w')
        
        baseline_col_input_frame = ttk.Frame(baseline_col_frame, style='Card.TFrame')
        baseline_col_input_frame.pack(fill='x', pady=(5, 0))
        
        ttk.Entry(baseline_col_input_frame, textvariable=self.baseline_start_col_var, 
                 style='Modern.TEntry', width=8).pack(side='left', padx=(0, 5))
        ttk.Label(baseline_col_input_frame, text="到", style='Normal.TLabel').pack(side='left', padx=5)
        ttk.Entry(baseline_col_input_frame, textvariable=self.baseline_end_col_var, 
                 style='Modern.TEntry', width=8).pack(side='left', padx=(5, 0))
        
        # 初始隐藏数据表计算范围
        self.baseline_range_frame.pack_forget()
        
    def on_baseline_method_change(self):
        """基准比例方法改变时的处理"""
        method = self.baseline_method_var.get()
        if method == "data_table":
            self.baseline_range_frame.pack(fill='x', padx=15, pady=5)
        else:
            self.baseline_range_frame.pack_forget()
            
    def on_closing(self):
        """窗口关闭时的处理"""
        if self.optimization_running:
            if messagebox.askokcancel("退出", "优化正在进行中，确定要退出吗？"):
                self.optimization_running = False
                self.root.destroy()
        else:
            self.root.destroy()
            
    def browse_file(self):
        """浏览文件"""
        filename = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[("Excel files", "*.xlsx *.xls")]
        )
        if filename:
            self.file_path_var.set(filename)
            self.load_sheet_names()
            
    def load_sheet_names(self):
        """加载工作表名称"""
        try:
            import openpyxl
            workbook = openpyxl.load_workbook(self.file_path_var.get(), read_only=True)
            sheet_names = workbook.sheetnames
            self.sheet_combo['values'] = sheet_names
            if sheet_names:
                self.sheet_name_var.set(sheet_names[0])
            workbook.close()
        except Exception as e:
            messagebox.showerror("错误", f"无法读取Excel文件: {str(e)}")
            
    def create_optimization_config_card(self):
        """创建优化配置卡片"""
        card = ttk.Frame(self.left_scrollable_frame, style='Card.TFrame')
        card.pack(fill='x', padx=5, pady=5)

        # 卡片标题
        title_frame = ttk.Frame(card, style='Card.TFrame')
        title_frame.pack(fill='x', padx=15, pady=(15, 10))

        ttk.Label(title_frame, text="⚙️ 优化配置", style='Title.TLabel').pack(anchor='w')

        # 总预算
        budget_frame = ttk.Frame(card, style='Card.TFrame')
        budget_frame.pack(fill='x', padx=15, pady=5)

        ttk.Label(budget_frame, text="总预算:", style='Normal.TLabel').pack(anchor='w')
        ttk.Entry(budget_frame, textvariable=self.total_budget_var,
                 style='Modern.TEntry').pack(fill='x', pady=(5, 0))

        # KPI类型
        kpi_frame = ttk.Frame(card, style='Card.TFrame')
        kpi_frame.pack(fill='x', padx=15, pady=5)

        ttk.Label(kpi_frame, text="KPI类型:", style='Normal.TLabel').pack(anchor='w')

        kpi_radio_frame = ttk.Frame(kpi_frame, style='Card.TFrame')
        kpi_radio_frame.pack(fill='x', pady=(5, 0))

        ttk.Radiobutton(kpi_radio_frame, text="Non-BHT (求和)",
                       variable=self.kpi_type_var, value="Non-BHT").pack(anchor='w')
        ttk.Radiobutton(kpi_radio_frame, text="BHT (平均)",
                       variable=self.kpi_type_var, value="BHT").pack(anchor='w')

        # 方案数量和迭代次数
        params_frame = ttk.Frame(card, style='Card.TFrame')
        params_frame.pack(fill='x', padx=15, pady=5)

        # 方案数量
        solutions_frame = ttk.Frame(params_frame, style='Card.TFrame')
        solutions_frame.pack(fill='x', pady=5)

        ttk.Label(solutions_frame, text="生成方案数:", style='Normal.TLabel').pack(anchor='w')
        ttk.Entry(solutions_frame, textvariable=self.num_solutions_var,
                 style='Modern.TEntry', width=10).pack(anchor='w', pady=(5, 0))

        # 迭代次数
        iterations_frame = ttk.Frame(params_frame, style='Card.TFrame')
        iterations_frame.pack(fill='x', pady=(5, 15))

        ttk.Label(iterations_frame, text="迭代次数:", style='Normal.TLabel').pack(anchor='w')
        ttk.Entry(iterations_frame, textvariable=self.iterations_var,
                 style='Modern.TEntry', width=10).pack(anchor='w', pady=(5, 0))

    def create_channel_constraints_card(self):
        """创建渠道约束卡片"""
        card = ttk.Frame(self.left_scrollable_frame, style='Card.TFrame')
        card.pack(fill='x', padx=5, pady=5)

        # 卡片标题
        title_frame = ttk.Frame(card, style='Card.TFrame')
        title_frame.pack(fill='x', padx=15, pady=(15, 10))

        ttk.Label(title_frame, text="🔒 渠道约束", style='Title.TLabel').pack(anchor='w')

        # 渠道列表和类型编辑
        self.channels_frame = ttk.Frame(card, style='Card.TFrame')
        self.channels_frame.pack(fill='x', padx=15, pady=5)

        # 读取渠道按钮
        read_channels_frame = ttk.Frame(self.channels_frame, style='Card.TFrame')
        read_channels_frame.pack(fill='x', pady=(0, 10))

        ttk.Button(read_channels_frame, text="读取渠道信息", style='Secondary.TButton',
                  command=self.read_channels).pack(anchor='w')

        # 渠道列表（动态生成）
        self.channels_list_frame = ttk.Frame(self.channels_frame, style='Card.TFrame')
        self.channels_list_frame.pack(fill='x', pady=5)

        # 开始优化按钮
        start_frame = ttk.Frame(card, style='Card.TFrame')
        start_frame.pack(fill='x', padx=15, pady=(10, 15))

        self.start_button = ttk.Button(start_frame, text="🚀 开始优化",
                                      style='Primary.TButton',
                                      command=self.start_optimization)
        self.start_button.pack(fill='x')

    def create_right_panel(self):
        """创建右侧监控面板"""
        # 右侧框架
        self.right_frame = ttk.Frame(self.main_container, style='Main.TFrame')

        # 创建监控卡片
        self.create_monitoring_card()
        self.create_results_preview_card()
        self.create_log_card()

    def create_monitoring_card(self):
        """创建实时监控卡片"""
        card = ttk.Frame(self.right_frame, style='Card.TFrame')
        card.pack(fill='x', padx=5, pady=5)

        # 卡片标题
        title_frame = ttk.Frame(card, style='Card.TFrame')
        title_frame.pack(fill='x', padx=15, pady=(15, 10))

        ttk.Label(title_frame, text="📊 实时监控", style='Title.TLabel').pack(anchor='w')

        # 监控指标
        metrics_frame = ttk.Frame(card, style='Card.TFrame')
        metrics_frame.pack(fill='x', padx=15, pady=5)

        # 创建监控指标网格
        self.create_metric_display(metrics_frame, "基准KPI", self.baseline_kpi_var, 0, 0)
        self.create_metric_display(metrics_frame, "当前最优", self.current_best_kpi_var, 0, 1)
        self.create_metric_display(metrics_frame, "提升幅度", self.improvement_var, 1, 0)
        self.create_metric_display(metrics_frame, "进度", self.progress_var, 1, 1)

        # 状态显示
        status_frame = ttk.Frame(card, style='Card.TFrame')
        status_frame.pack(fill='x', padx=15, pady=(10, 15))

        ttk.Label(status_frame, text="状态:", style='Normal.TLabel').pack(anchor='w')
        self.status_label = ttk.Label(status_frame, textvariable=self.status_var,
                                     style='Subtitle.TLabel')
        self.status_label.pack(anchor='w', pady=(5, 0))

    def create_metric_display(self, parent, label, variable, row, col):
        """创建监控指标显示"""
        metric_frame = ttk.Frame(parent, style='Card.TFrame')
        metric_frame.grid(row=row, column=col, padx=10, pady=10, sticky='ew')

        # 配置网格权重
        parent.grid_columnconfigure(col, weight=1)

        ttk.Label(metric_frame, text=label, style='Normal.TLabel').pack(anchor='w')

        value_label = ttk.Label(metric_frame, textvariable=variable,
                               style='Title.TLabel')
        value_label.pack(anchor='w', pady=(5, 0))

    def create_results_preview_card(self):
        """创建结果预览卡片"""
        card = ttk.Frame(self.right_frame, style='Card.TFrame')
        card.pack(fill='both', expand=True, padx=5, pady=5)

        # 卡片标题
        title_frame = ttk.Frame(card, style='Card.TFrame')
        title_frame.pack(fill='x', padx=15, pady=(15, 10))

        ttk.Label(title_frame, text="🏆 优化结果预览", style='Title.TLabel').pack(anchor='w')

        # 结果显示区域
        self.results_text = scrolledtext.ScrolledText(
            card,
            wrap=tk.WORD,
            height=15,
            font=('Microsoft YaHei UI', 9),
            bg='white',
            fg=self.colors['text']
        )
        self.results_text.pack(fill='both', expand=True, padx=15, pady=(0, 15))

    def create_log_card(self):
        """创建日志卡片"""
        card = ttk.Frame(self.right_frame, style='Card.TFrame')
        card.pack(fill='x', padx=5, pady=5)

        # 卡片标题
        title_frame = ttk.Frame(card, style='Card.TFrame')
        title_frame.pack(fill='x', padx=15, pady=(15, 10))

        ttk.Label(title_frame, text="📝 运行日志", style='Title.TLabel').pack(anchor='w')

        # 日志显示区域
        self.log_text = scrolledtext.ScrolledText(
            card,
            wrap=tk.WORD,
            height=8,
            font=('Consolas', 8),
            bg='#2C3E50',
            fg='#ECF0F1'
        )
        self.log_text.pack(fill='x', padx=15, pady=(0, 15))

    def setup_layout(self):
        """设置布局"""
        # 配置左右分栏
        self.left_canvas.pack(side='left', fill='both', expand=True, padx=(0, 5))
        self.left_scrollbar.pack(side='left', fill='y')
        self.right_frame.pack(side='right', fill='both', expand=True, padx=(5, 0))

        # 设置权重比例 (左:右 = 1:1)
        self.main_container.grid_columnconfigure(0, weight=1)
        self.main_container.grid_columnconfigure(1, weight=1)

    def setup_bindings(self):
        """设置事件绑定"""
        # 鼠标滚轮绑定
        self.bind_mousewheel(self.left_canvas)

        # 窗口大小改变时的处理
        self.root.bind('<Configure>', self.on_window_resize)

    def bind_mousewheel(self, widget):
        """绑定鼠标滚轮事件"""
        def _on_mousewheel(event):
            widget.yview_scroll(int(-1*(event.delta/120)), "units")

        widget.bind("<MouseWheel>", _on_mousewheel)

    def on_window_resize(self, event):
        """窗口大小改变时的处理"""
        if event.widget == self.root:
            # 更新左侧画布的滚动区域
            self.left_canvas.configure(scrollregion=self.left_canvas.bbox("all"))

    def read_channels(self):
        """读取渠道信息"""
        try:
            # 验证必要参数
            if not self.file_path_var.get():
                messagebox.showerror("错误", "请先选择Excel文件")
                return

            if not self.sheet_name_var.get():
                messagebox.showerror("错误", "请选择工作表")
                return

            if not all([self.data_start_col_var.get(), self.data_end_col_var.get(),
                       self.channel_row_var.get()]):
                messagebox.showerror("错误", "请填写完整的数据范围信息")
                return

            # 读取渠道信息
            from universal_optimizer_engine_v4 import MediaOptimizerEngineV4

            engine = MediaOptimizerEngineV4()
            config = {
                'file_path': self.file_path_var.get(),
                'sheet_name': self.sheet_name_var.get(),
                'data_range': {
                    'start_col': self.data_start_col_var.get(),
                    'end_col': self.data_end_col_var.get(),
                    'channel_row': int(self.channel_row_var.get())
                }
            }
            engine.set_config(config)

            if engine.read_channels():
                self.channels_data = engine.channels
                self.display_channels_list()
                self.add_log(f"✅ 成功读取 {len(self.channels_data)} 个渠道")
            else:
                messagebox.showerror("错误", "读取渠道信息失败")

        except Exception as e:
            messagebox.showerror("错误", f"读取渠道失败: {str(e)}")
            self.add_log(f"❌ 读取渠道失败: {str(e)}")

    def display_channels_list(self):
        """显示渠道列表供编辑"""
        # 清除现有内容
        for widget in self.channels_list_frame.winfo_children():
            widget.destroy()

        if not self.channels_data:
            return

        # 创建表头
        header_frame = ttk.Frame(self.channels_list_frame, style='Card.TFrame')
        header_frame.pack(fill='x', pady=(0, 10))

        ttk.Label(header_frame, text="渠道列表与类型设置", style='Subtitle.TLabel').pack(anchor='w')

        # 创建滚动框架
        canvas = tk.Canvas(self.channels_list_frame, height=200, bg=self.colors['surface'])
        scrollbar = ttk.Scrollbar(self.channels_list_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas, style='Card.TFrame')

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # 渠道类型选项
        channel_type_options = ["数字媒体", "社交媒体", "传统媒体", "户外媒体", "其他"]

        # 为每个渠道创建编辑行
        self.channel_widgets = {}
        for i, channel in enumerate(self.channels_data):
            row_frame = ttk.Frame(scrollable_frame, style='Card.TFrame')
            row_frame.pack(fill='x', padx=5, pady=2)

            # 渠道名称
            name_label = ttk.Label(row_frame, text=channel, style='Normal.TLabel', width=15)
            name_label.pack(side='left', padx=(0, 10))

            # 渠道类型选择
            type_var = tk.StringVar(value=self.channel_types.get(channel, "数字媒体"))
            type_combo = ttk.Combobox(row_frame, textvariable=type_var,
                                     values=channel_type_options,
                                     style='Modern.TCombobox', width=12, state='readonly')
            type_combo.pack(side='left', padx=(0, 10))

            # 约束设置
            constraint_frame = ttk.Frame(row_frame, style='Card.TFrame')
            constraint_frame.pack(side='left', fill='x', expand=True)

            ttk.Label(constraint_frame, text="比例范围:", style='Normal.TLabel').pack(side='left', padx=(0, 5))

            min_var = tk.StringVar(value="2")
            max_var = tk.StringVar(value="40")

            min_entry = ttk.Entry(constraint_frame, textvariable=min_var,
                                 style='Modern.TEntry', width=6)
            min_entry.pack(side='left', padx=(0, 2))

            ttk.Label(constraint_frame, text="% -", style='Normal.TLabel').pack(side='left', padx=2)

            max_entry = ttk.Entry(constraint_frame, textvariable=max_var,
                                 style='Modern.TEntry', width=6)
            max_entry.pack(side='left', padx=(2, 0))

            ttk.Label(constraint_frame, text="%", style='Normal.TLabel').pack(side='left', padx=(2, 0))

            # 保存控件引用
            self.channel_widgets[channel] = {
                'type_var': type_var,
                'min_var': min_var,
                'max_var': max_var
            }

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 绑定鼠标滚轮
        self.bind_mousewheel(canvas)

    def start_optimization(self):
        """开始优化"""
        try:
            # 验证参数
            if not self.validate_optimization_params():
                return

            # 收集配置
            config = self.collect_optimization_config()

            # 禁用开始按钮
            self.start_button.configure(state='disabled', text="优化中...")

            # 清空结果和日志
            self.results_text.delete(1.0, tk.END)
            self.log_text.delete(1.0, tk.END)

            # 重置监控数据
            self.baseline_kpi_var.set("0")
            self.current_best_kpi_var.set("0")
            self.improvement_var.set("0%")
            self.progress_var.set("0%")
            self.status_var.set("准备中...")

            # 在新线程中运行优化
            self.optimization_thread = threading.Thread(
                target=self.run_optimization_thread,
                args=(config,)
            )
            self.optimization_thread.daemon = True
            self.optimization_thread.start()

        except Exception as e:
            messagebox.showerror("错误", f"启动优化失败: {str(e)}")
            self.add_log(f"❌ 启动优化失败: {str(e)}")
            self.start_button.configure(state='normal', text="🚀 开始优化")

    def validate_optimization_params(self):
        """验证优化参数"""
        try:
            # 检查基本参数
            if not self.channels_data:
                messagebox.showerror("错误", "请先读取渠道信息")
                return False

            if not self.total_budget_var.get():
                messagebox.showerror("错误", "请输入总预算")
                return False

            try:
                budget = float(self.total_budget_var.get())
                if budget <= 0:
                    raise ValueError("预算必须大于0")
            except ValueError:
                messagebox.showerror("错误", "请输入有效的总预算")
                return False

            # 检查方案数和迭代次数
            try:
                num_solutions = int(self.num_solutions_var.get() or "5")
                iterations = int(self.iterations_var.get() or "100")

                if num_solutions <= 0 or iterations <= 0:
                    raise ValueError("方案数和迭代次数必须大于0")

            except ValueError:
                messagebox.showerror("错误", "请输入有效的方案数和迭代次数")
                return False

            # 检查数据范围
            required_fields = [
                (self.data_start_row_var.get(), "数据起始行"),
                (self.data_end_row_var.get(), "数据结束行"),
                (self.data_start_col_var.get(), "数据起始列"),
                (self.data_end_col_var.get(), "数据结束列")
            ]

            for field_value, field_name in required_fields:
                if not field_value:
                    messagebox.showerror("错误", f"请填写{field_name}")
                    return False

            return True

        except Exception as e:
            messagebox.showerror("错误", f"参数验证失败: {str(e)}")
            return False

    def collect_optimization_config(self):
        """收集优化配置"""
        # 收集渠道类型和约束
        channel_types = {}
        channel_constraints = {}

        for channel, widgets in self.channel_widgets.items():
            channel_types[channel] = widgets['type_var'].get()

            try:
                min_ratio = float(widgets['min_var'].get()) / 100.0
                max_ratio = float(widgets['max_var'].get()) / 100.0

                # 确保约束合理
                min_ratio = max(0.0, min(min_ratio, 1.0))
                max_ratio = max(min_ratio, min(max_ratio, 1.0))

                channel_constraints[channel] = {
                    'min': min_ratio,
                    'max': max_ratio
                }
            except ValueError:
                # 使用默认约束
                channel_constraints[channel] = {'min': 0.02, 'max': 0.40}

        # 基准比例配置
        baseline_config = {
            'method': self.baseline_method_var.get()
        }

        if baseline_config['method'] == 'data_table':
            baseline_config.update({
                'start_row': int(self.baseline_start_row_var.get() or "1"),
                'end_row': int(self.baseline_end_row_var.get() or "1"),
                'start_col': self.baseline_start_col_var.get() or "A",
                'end_col': self.baseline_end_col_var.get() or "Z"
            })

        config = {
            'file_path': self.file_path_var.get(),
            'sheet_name': self.sheet_name_var.get(),
            'data_range': {
                'start_row': int(self.data_start_row_var.get()),
                'end_row': int(self.data_end_row_var.get()),
                'start_col': self.data_start_col_var.get(),
                'end_col': self.data_end_col_var.get(),
                'channel_row': int(self.channel_row_var.get()),
                'budget_row': int(self.data_start_row_var.get()) - 1  # 假设预算在数据上一行
            },
            'baseline_config': baseline_config,
            'optimization_config': {
                'total_budget': float(self.total_budget_var.get()),
                'num_solutions': int(self.num_solutions_var.get() or "5"),
                'iterations': int(self.iterations_var.get() or "100"),
                'kpi_type': self.kpi_type_var.get()
            },
            'channel_constraints': channel_constraints,
            'channel_types': channel_types
        }

        return config

    def run_optimization_thread(self, config):
        """在线程中运行优化"""
        try:
            from universal_optimizer_engine_v4 import MediaOptimizerEngineV4

            # 创建优化引擎
            engine = MediaOptimizerEngineV4(progress_callback=self.update_progress)
            engine.set_config(config)

            # 运行优化
            self.optimization_running = True
            success = engine.run_optimization()

            if success:
                # 获取结果
                results = engine.get_results()

                # 在主线程中更新界面
                self.root.after(0, self.on_optimization_complete, results)
            else:
                self.root.after(0, self.on_optimization_error, "优化过程中发生错误")

        except Exception as e:
            self.root.after(0, self.on_optimization_error, str(e))
        finally:
            self.optimization_running = False

    def update_progress(self, progress, status, detail):
        """更新进度（线程安全）"""
        self.root.after(0, self._update_progress_ui, progress, status, detail)

    def _update_progress_ui(self, progress, status, detail):
        """更新进度界面"""
        try:
            # 更新状态
            self.status_var.set(status)
            self.progress_var.set(f"{progress}%")

            # 解析详细信息
            if "基准KPI" in detail:
                # 修复：正确解析包含千分位分隔符的KPI值
                kpi_part = detail.split("基准KPI: ")[1]
                kpi_match = re.search(r'[\d,]+\.?\d*', kpi_part)
                if kpi_match:
                    kpi_value = kpi_match.group()
                    self.baseline_kpi_var.set(kpi_value)

            elif "当前最优KPI" in detail:
                # 解析当前最优KPI和提升幅度
                parts = detail.split(", ")
                for part in parts:
                    if "当前最优KPI:" in part:
                        kpi_match = re.search(r'[\d,]+\.?\d*', part)
                        if kpi_match:
                            self.current_best_kpi_var.set(kpi_match.group())
                    elif "提升:" in part:
                        improvement_match = re.search(r'[\d.]+%', part)
                        if improvement_match:
                            self.improvement_var.set(improvement_match.group())

            elif "最优KPI" in detail:
                # 最终结果
                parts = detail.split(", ")
                for part in parts:
                    if "最优KPI:" in part:
                        kpi_match = re.search(r'[\d,]+\.?\d*', part)
                        if kpi_match:
                            self.current_best_kpi_var.set(kpi_match.group())
                    elif "总提升:" in part:
                        improvement_match = re.search(r'[\d.]+%', part)
                        if improvement_match:
                            self.improvement_var.set(improvement_match.group())

            # 添加到日志
            if status == "日志":
                self.add_log(detail)
            else:
                self.add_log(f"{status}: {detail}")

        except Exception as e:
            self.add_log(f"❌ 更新进度失败: {str(e)}")

    def on_optimization_complete(self, results):
        """优化完成处理"""
        try:
            self.add_log("✅ 优化完成！")

            # 显示结果预览
            self.display_results_preview(results)

            # 恢复开始按钮
            self.start_button.configure(state='normal', text="🚀 开始优化")

            # 显示保存选项
            self.show_save_options(results)

        except Exception as e:
            self.add_log(f"❌ 处理优化结果失败: {str(e)}")
            self.start_button.configure(state='normal', text="🚀 开始优化")

    def on_optimization_error(self, error_message):
        """优化错误处理"""
        self.add_log(f"❌ 优化失败: {error_message}")
        self.start_button.configure(state='normal', text="🚀 开始优化")
        messagebox.showerror("优化失败", f"优化过程中发生错误:\n{error_message}")

    def display_results_preview(self, results):
        """显示结果预览（简化版）"""
        try:
            self.results_text.delete(1.0, tk.END)

            # 标题
            self.results_text.insert(tk.END, "🏆 优化结果预览\n", "title")
            self.results_text.insert(tk.END, "=" * 50 + "\n\n", "separator")

            # 基本信息
            baseline_kpi = results.get('baseline_kpi', 0)
            best_kpi = results.get('best_kpi', 0)
            improvement = ((best_kpi - baseline_kpi) / baseline_kpi * 100) if baseline_kpi > 0 else 0

            self.results_text.insert(tk.END, f"📈 基准KPI: {baseline_kpi:,.2f}\n", "normal")
            self.results_text.insert(tk.END, f"🎯 最优KPI: {best_kpi:,.2f}\n", "highlight")
            self.results_text.insert(tk.END, f"📊 提升幅度: {improvement:.2f}%\n\n", "success")

            # 显示前3个最优方案的关键信息
            solutions = results.get('solutions', [])[:3]

            self.results_text.insert(tk.END, "🏅 最优方案预览:\n", "subtitle")
            self.results_text.insert(tk.END, "-" * 30 + "\n", "separator")

            for i, solution in enumerate(solutions, 1):
                kpi = solution.get('kpi', 0)
                improvement = ((kpi - baseline_kpi) / baseline_kpi * 100) if baseline_kpi > 0 else 0

                self.results_text.insert(tk.END, f"\n方案 {i}:\n", "subtitle")
                self.results_text.insert(tk.END, f"  KPI: {kpi:,.2f} (提升 {improvement:.1f}%)\n", "normal")

                # 显示主要渠道分配（只显示占比最大的3个）
                ratios = solution.get('ratios', {})
                sorted_ratios = sorted(ratios.items(), key=lambda x: x[1], reverse=True)[:3]

                self.results_text.insert(tk.END, "  主要分配:\n", "normal")
                for channel, ratio in sorted_ratios:
                    self.results_text.insert(tk.END, f"    {channel}: {ratio*100:.1f}%\n", "normal")

            self.results_text.insert(tk.END, f"\n\n💾 共生成 {len(results.get('solutions', []))} 个优化方案\n", "info")
            self.results_text.insert(tk.END, "点击下方按钮保存详细结果到Excel文件", "info")

            # 配置文本样式
            self.configure_text_tags()

        except Exception as e:
            self.add_log(f"❌ 显示结果预览失败: {str(e)}")

    def configure_text_tags(self):
        """配置文本标签样式"""
        self.results_text.tag_configure("title",
                                        font=('Microsoft YaHei UI', 12, 'bold'),
                                        foreground=self.colors['primary'])
        self.results_text.tag_configure("subtitle",
                                        font=('Microsoft YaHei UI', 10, 'bold'),
                                        foreground=self.colors['secondary'])
        self.results_text.tag_configure("highlight",
                                        font=('Microsoft YaHei UI', 10, 'bold'),
                                        foreground=self.colors['accent'])
        self.results_text.tag_configure("success",
                                        font=('Microsoft YaHei UI', 10, 'bold'),
                                        foreground=self.colors['success'])
        self.results_text.tag_configure("normal",
                                        font=('Microsoft YaHei UI', 9),
                                        foreground=self.colors['text'])
        self.results_text.tag_configure("info",
                                        font=('Microsoft YaHei UI', 9),
                                        foreground=self.colors['text_light'])
        self.results_text.tag_configure("separator",
                                        foreground=self.colors['border'])

    def show_save_options(self, results):
        """显示保存选项"""
        try:
            # 创建保存对话框
            save_dialog = tk.Toplevel(self.root)
            save_dialog.title("保存优化结果")
            save_dialog.geometry("400x200")
            save_dialog.transient(self.root)
            save_dialog.grab_set()

            # 居中显示
            save_dialog.update_idletasks()
            x = (save_dialog.winfo_screenwidth() // 2) - (400 // 2)
            y = (save_dialog.winfo_screenheight() // 2) - (200 // 2)
            save_dialog.geometry(f"400x200+{x}+{y}")

            # 内容
            main_frame = ttk.Frame(save_dialog, style='Card.TFrame')
            main_frame.pack(fill='both', expand=True, padx=20, pady=20)

            ttk.Label(main_frame, text="优化完成！",
                     style='Title.TLabel').pack(pady=(0, 10))

            ttk.Label(main_frame, text="是否保存结果到Excel文件？",
                     style='Normal.TLabel').pack(pady=(0, 20))

            # 按钮
            button_frame = ttk.Frame(main_frame, style='Card.TFrame')
            button_frame.pack(fill='x')

            ttk.Button(button_frame, text="保存结果", style='Primary.TButton',
                      command=lambda: self.save_results(results, save_dialog)).pack(side='left', padx=(0, 10))

            ttk.Button(button_frame, text="暂不保存", style='Secondary.TButton',
                      command=save_dialog.destroy).pack(side='left')

        except Exception as e:
            self.add_log(f"❌ 显示保存选项失败: {str(e)}")

    def save_results(self, results, dialog):
        """保存结果"""
        try:
            # 选择保存位置
            filename = filedialog.asksaveasfilename(
                title="保存优化结果",
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx")]
            )

            if filename:
                # 这里实现保存逻辑
                self.add_log(f"💾 结果已保存到: {filename}")
                dialog.destroy()
                messagebox.showinfo("保存成功", f"优化结果已保存到:\n{filename}")
            else:
                dialog.destroy()

        except Exception as e:
            self.add_log(f"❌ 保存结果失败: {str(e)}")
            messagebox.showerror("保存失败", f"保存结果时发生错误:\n{str(e)}")

    def add_log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"

        self.log_text.insert(tk.END, log_message)
        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def create_fallback_interface(self):
        """创建备用简化界面"""
        try:
            # 清除现有内容
            for widget in self.main_container.winfo_children():
                widget.destroy()

            # 创建简化界面
            title_label = tk.Label(self.main_container,
                                 text="媒体预算优化工具 v4.0 - 简化模式",
                                 font=('Microsoft YaHei UI', 16, 'bold'),
                                 bg='#f0f0f0', fg='#2E86AB')
            title_label.pack(pady=20)

            info_label = tk.Label(self.main_container,
                                text="程序正在简化模式下运行\n完整功能正在开发中",
                                font=('Microsoft YaHei UI', 10),
                                bg='#f0f0f0', fg='#666666')
            info_label.pack(pady=10)

            # 文件选择
            file_frame = tk.Frame(self.main_container, bg='white', relief='solid', bd=1)
            file_frame.pack(fill='x', padx=20, pady=10)

            tk.Label(file_frame, text="Excel文件:", font=('Microsoft YaHei UI', 10),
                    bg='white').pack(anchor='w', padx=10, pady=(10, 5))

            file_input_frame = tk.Frame(file_frame, bg='white')
            file_input_frame.pack(fill='x', padx=10, pady=(0, 10))

            self.file_entry = tk.Entry(file_input_frame, textvariable=self.file_path_var,
                                     state='readonly', font=('Microsoft YaHei UI', 9))
            self.file_entry.pack(side='left', fill='x', expand=True, padx=(0, 10))

            tk.Button(file_input_frame, text="浏览", command=self.browse_file,
                     font=('Microsoft YaHei UI', 9)).pack(side='right')

            # 状态显示
            status_frame = tk.Frame(self.main_container, bg='white', relief='solid', bd=1)
            status_frame.pack(fill='x', padx=20, pady=10)

            tk.Label(status_frame, text="状态:", font=('Microsoft YaHei UI', 10),
                    bg='white').pack(side='left', padx=10, pady=10)
            tk.Label(status_frame, textvariable=self.status_var,
                    font=('Microsoft YaHei UI', 10), bg='white').pack(side='left', padx=10, pady=10)

            print("✅ 备用界面创建成功")

        except Exception as e:
            print(f"❌ 备用界面创建失败: {e}")
            # 最基本的界面
            tk.Label(self.main_container, text="程序启动成功！",
                    font=('Microsoft YaHei UI', 14)).pack(pady=50)

    def run(self):
        """运行应用"""
        try:
            print("启动主循环...")
            self.root.mainloop()
            print("程序正常退出")
        except Exception as e:
            print(f"运行失败: {e}")
            import traceback
            traceback.print_exc()

def main():
    """主函数"""
    try:
        print("开始启动媒体预算优化工具 v4.0...")
        app = MediaOptimizerV4()
        app.run()
    except Exception as e:
        print(f"程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")

if __name__ == "__main__":
    main()
