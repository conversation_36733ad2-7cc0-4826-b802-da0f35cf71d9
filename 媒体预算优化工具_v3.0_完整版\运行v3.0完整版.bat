@echo off
chcp 65001 >nul
echo.
echo ========================================
echo   媒体预算优化工具 v3.0 完整版启动器
echo ========================================
echo.
echo 正在启动v3.0完整版（包含所有新功能）...
echo.
echo 新功能包括：
echo - KPI类型选择（BHT/Non-BHT）
echo - 16:9屏幕优化布局
echo - 实时监控仪表板
echo - 滑块配比设置
echo - 美化Excel输出
echo - 列字母输入
echo.

python universal_media_optimizer_v2.py

if %errorlevel% neq 0 (
    echo.
    echo ❌ 启动失败！可能的原因：
    echo 1. 未安装Python
    echo 2. 缺少必要的库（win32com, tkinter等）
    echo.
    echo 💡 解决方案：
    echo 1. 安装Python 3.7+
    echo 2. 运行：pip install pywin32
    echo 3. 或者使用exe文件（功能较少）
    echo.
    pause
) else (
    echo.
    echo ✅ 程序正常退出
)
