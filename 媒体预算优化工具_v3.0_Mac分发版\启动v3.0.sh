#!/bin/bash

# 媒体预算优化工具 v3.0 Mac版启动脚本

echo ""
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                媒体预算优化工具 v3.0 Mac版                  ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo ""
echo "🆕 v3.0 新功能："
echo "  • KPI类型选择（BHT/Non-BHT）"
echo "  • 16:9屏幕优化布局"
echo "  • 实时监控仪表板"
echo "  • 跨平台兼容性"
echo "  • Excel文件读取（openpyxl）"
echo ""

# 检查Python
if ! command -v python3 &> /dev/null; then
    echo "❌ 未检测到Python3环境！"
    echo ""
    echo "💡 解决方案："
    echo "1. 安装Python 3.7+：https://www.python.org/downloads/"
    echo "2. 或使用Homebrew：brew install python3"
    echo ""
    read -p "按回车键退出..."
    exit 1
fi

echo "✅ Python环境检测通过"

# 检查依赖
echo "🔍 检查依赖库..."

python3 -c "import openpyxl" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "⚠️ 缺少openpyxl库，正在安装..."
    python3 -m pip install openpyxl
    if [ $? -ne 0 ]; then
        echo "❌ 依赖安装失败！"
        echo "请手动运行：pip3 install openpyxl"
        read -p "按回车键退出..."
        exit 1
    fi
fi

python3 -c "import tkinter" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "❌ 缺少tkinter库！"
    echo "请安装：sudo apt-get install python3-tk (Ubuntu/Debian)"
    echo "或：brew install python-tk (macOS)"
    read -p "按回车键退出..."
    exit 1
fi

echo "✅ 依赖检查完成"

# 启动程序
echo "🚀 启动媒体预算优化工具 v3.0..."
echo ""

python3 universal_media_optimizer_mac.py

if [ $? -ne 0 ]; then
    echo ""
    echo "❌ 程序启动失败！"
    echo ""
    echo "💡 可能的解决方案："
    echo "1. 确保所有文件完整"
    echo "2. 检查文件权限"
    echo "3. 查看错误信息"
    echo ""
    read -p "按回车键退出..."
fi

echo ""
echo "✅ 程序已退出"
