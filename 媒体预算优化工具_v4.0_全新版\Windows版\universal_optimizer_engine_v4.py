#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
媒体预算优化引擎 v4.0 - 全新版
修复约束逻辑，实现实时监控，支持基准比例计算
"""

import win32com.client
import os
import random
import time
from datetime import datetime
import json
import re

class MediaOptimizerEngineV4:
    def __init__(self, progress_callback=None):
        self.progress_callback = progress_callback
        self.excel_app = None
        self.workbook = None
        self.worksheet = None
        
        # 配置参数
        self.file_path = ""
        self.sheet_name = ""
        self.data_range = {}
        self.baseline_config = {}
        self.optimization_config = {}
        self.channel_constraints = {}
        self.channel_types = {}
        
        # 运行时数据
        self.channels = []
        self.baseline_ratios = {}
        self.baseline_kpi = 0
        self.current_best_kpi = 0
        self.current_best_solution = None
        self.all_solutions = []
        self.is_running = False
        
    def set_config(self, config):
        """设置配置参数"""
        self.file_path = config.get('file_path', '')
        self.sheet_name = config.get('sheet_name', '')
        self.data_range = config.get('data_range', {})
        self.baseline_config = config.get('baseline_config', {})
        self.optimization_config = config.get('optimization_config', {})
        self.channel_constraints = config.get('channel_constraints', {})
        self.channel_types = config.get('channel_types', {})
        
    def connect_excel(self):
        """连接Excel应用"""
        try:
            self.excel_app = win32com.client.Dispatch("Excel.Application")
            self.excel_app.Visible = False
            self.excel_app.DisplayAlerts = False
            
            self.workbook = self.excel_app.Workbooks.Open(self.file_path)
            self.worksheet = self.workbook.Worksheets(self.sheet_name)
            
            self.log("✅ Excel连接成功")
            return True
            
        except Exception as e:
            self.log(f"❌ Excel连接失败: {str(e)}")
            return False
            
    def disconnect_excel(self):
        """断开Excel连接"""
        try:
            if self.workbook:
                self.workbook.Close(SaveChanges=False)
            if self.excel_app:
                self.excel_app.Quit()
                
            self.log("✅ Excel连接已断开")
            
        except Exception as e:
            self.log(f"⚠️ 断开Excel连接时出错: {str(e)}")
            
    def read_channels(self):
        """读取渠道信息"""
        try:
            if not self.connect_excel():
                return False
                
            # 读取渠道名称
            channel_row = self.data_range.get('channel_row', 1)
            start_col = self.column_letter_to_number(self.data_range.get('start_col', 'A'))
            end_col = self.column_letter_to_number(self.data_range.get('end_col', 'Z'))
            
            self.channels = []
            for col in range(start_col, end_col + 1):
                cell_value = self.worksheet.Cells(channel_row, col).Value
                if cell_value and str(cell_value).strip():
                    self.channels.append(str(cell_value).strip())
                    
            self.log(f"📊 读取到 {len(self.channels)} 个渠道")
            return True
            
        except Exception as e:
            self.log(f"❌ 读取渠道失败: {str(e)}")
            return False
            
    def calculate_baseline_ratios(self):
        """计算基准比例"""
        try:
            method = self.baseline_config.get('method', 'average')
            
            if method == 'average':
                # 平均分配
                ratio = 1.0 / len(self.channels)
                self.baseline_ratios = {channel: ratio for channel in self.channels}
                self.log("📊 使用平均分配基准比例")
                
            elif method == 'data_table':
                # 从数据表计算
                self.baseline_ratios = self.calculate_ratios_from_data()
                self.log("📊 从数据表计算基准比例")
                
            elif method == 'manual':
                # 手动设置（从配置中获取）
                self.baseline_ratios = self.baseline_config.get('ratios', {})
                self.log("📊 使用手动设置基准比例")
                
            # 验证比例总和
            total_ratio = sum(self.baseline_ratios.values())
            if abs(total_ratio - 1.0) > 0.001:
                # 标准化比例
                for channel in self.baseline_ratios:
                    self.baseline_ratios[channel] /= total_ratio
                self.log("⚠️ 基准比例已标准化")
                
            return True
            
        except Exception as e:
            self.log(f"❌ 计算基准比例失败: {str(e)}")
            return False
            
    def calculate_ratios_from_data(self):
        """从数据表计算比例"""
        try:
            start_row = self.baseline_config.get('start_row', 1)
            end_row = self.baseline_config.get('end_row', 1)
            start_col = self.column_letter_to_number(self.baseline_config.get('start_col', 'A'))
            end_col = self.column_letter_to_number(self.baseline_config.get('end_col', 'Z'))
            
            # 读取数据并计算总和
            channel_totals = {}
            total_sum = 0
            
            for col_idx, channel in enumerate(self.channels):
                col = start_col + col_idx
                if col > end_col:
                    break
                    
                channel_sum = 0
                for row in range(start_row, end_row + 1):
                    cell_value = self.worksheet.Cells(row, col).Value
                    if cell_value and isinstance(cell_value, (int, float)):
                        channel_sum += cell_value
                        
                channel_totals[channel] = channel_sum
                total_sum += channel_sum
                
            # 计算比例
            ratios = {}
            if total_sum > 0:
                for channel in self.channels:
                    ratios[channel] = channel_totals.get(channel, 0) / total_sum
            else:
                # 如果总和为0，使用平均分配
                ratio = 1.0 / len(self.channels)
                ratios = {channel: ratio for channel in self.channels}
                
            return ratios
            
        except Exception as e:
            self.log(f"❌ 从数据表计算比例失败: {str(e)}")
            return {}
            
    def calculate_baseline_kpi(self):
        """计算基准KPI"""
        try:
            # 根据基准比例分配预算
            total_budget = self.optimization_config.get('total_budget', 1000000)
            baseline_budgets = {}
            
            for channel in self.channels:
                ratio = self.baseline_ratios.get(channel, 0)
                baseline_budgets[channel] = total_budget * ratio
                
            # 写入Excel并计算KPI
            self.baseline_kpi = self.write_budgets_and_calculate_kpi(baseline_budgets)
            
            self.log(f"📈 基准KPI: {self.baseline_kpi:,.2f}")
            
            # 更新监控界面
            if self.progress_callback:
                self.progress_callback(10, "基准KPI计算完成", 
                                     f"基准KPI: {self.baseline_kpi:,.2f}")
                                     
            return True
            
        except Exception as e:
            self.log(f"❌ 计算基准KPI失败: {str(e)}")
            return False
            
    def write_budgets_and_calculate_kpi(self, budgets):
        """写入预算并计算KPI"""
        try:
            # 写入预算到Excel
            start_col = self.column_letter_to_number(self.data_range.get('start_col', 'A'))
            budget_row = self.data_range.get('budget_row', 2)  # 假设预算在第2行
            
            for col_idx, channel in enumerate(self.channels):
                col = start_col + col_idx
                budget = budgets.get(channel, 0)
                self.worksheet.Cells(budget_row, col).Value = budget
                
            # 计算KPI
            kpi_value = self.calculate_kpi()
            return kpi_value
            
        except Exception as e:
            self.log(f"❌ 写入预算并计算KPI失败: {str(e)}")
            return 0
            
    def calculate_kpi(self):
        """计算KPI值"""
        try:
            kpi_type = self.optimization_config.get('kpi_type', 'Non-BHT')
            start_row = self.data_range.get('start_row', 1)
            end_row = self.data_range.get('end_row', 10)
            start_col = self.column_letter_to_number(self.data_range.get('start_col', 'A'))
            end_col = self.column_letter_to_number(self.data_range.get('end_col', 'Z'))
            
            if kpi_type == "BHT":
                # BHT: 计算平均值
                total_kpi = 0
                valid_rows = 0
                
                for row in range(start_row, end_row + 1):
                    row_sum = 0
                    valid_cols = 0
                    
                    for col in range(start_col, end_col + 1):
                        cell_value = self.worksheet.Cells(row, col).Value
                        if cell_value and isinstance(cell_value, (int, float)):
                            row_sum += cell_value
                            valid_cols += 1
                            
                    if valid_cols > 0:
                        total_kpi += row_sum / valid_cols
                        valid_rows += 1
                        
                return total_kpi / valid_rows if valid_rows > 0 else 0
                
            else:
                # Non-BHT: 计算总和
                total_kpi = 0
                
                for row in range(start_row, end_row + 1):
                    for col in range(start_col, end_col + 1):
                        cell_value = self.worksheet.Cells(row, col).Value
                        if cell_value and isinstance(cell_value, (int, float)):
                            total_kpi += cell_value
                            
                return total_kpi
                
        except Exception as e:
            self.log(f"❌ 计算KPI失败: {str(e)}")
            return 0
            
    def generate_random_solution(self):
        """生成随机解决方案（考虑约束）"""
        try:
            max_attempts = 1000
            
            for attempt in range(max_attempts):
                # 生成随机比例
                ratios = {}
                remaining = 1.0
                
                # 按约束生成比例
                for i, channel in enumerate(self.channels[:-1]):
                    constraint = self.channel_constraints.get(channel, {})
                    min_ratio = constraint.get('min', 0.01)
                    max_ratio = constraint.get('max', 0.99)
                    
                    # 确保不超过剩余比例
                    max_ratio = min(max_ratio, remaining - 0.01 * (len(self.channels) - i - 1))
                    min_ratio = min(min_ratio, max_ratio)
                    
                    if max_ratio <= min_ratio:
                        ratio = min_ratio
                    else:
                        ratio = random.uniform(min_ratio, max_ratio)
                        
                    ratios[channel] = ratio
                    remaining -= ratio
                    
                # 最后一个渠道获得剩余比例
                last_channel = self.channels[-1]
                ratios[last_channel] = remaining
                
                # 验证约束
                if self.validate_constraints(ratios):
                    return ratios
                    
            # 如果无法生成有效解，返回基准比例
            self.log("⚠️ 无法生成满足约束的随机解，使用基准比例")
            return self.baseline_ratios.copy()
            
        except Exception as e:
            self.log(f"❌ 生成随机解失败: {str(e)}")
            return self.baseline_ratios.copy()
            
    def validate_constraints(self, ratios):
        """验证约束条件"""
        try:
            # 检查比例总和
            total_ratio = sum(ratios.values())
            if abs(total_ratio - 1.0) > 0.001:
                return False
                
            # 检查每个渠道的约束
            for channel, ratio in ratios.items():
                constraint = self.channel_constraints.get(channel, {})
                min_ratio = constraint.get('min', 0)
                max_ratio = constraint.get('max', 1)
                
                if ratio < min_ratio or ratio > max_ratio:
                    return False
                    
            return True
            
        except Exception as e:
            self.log(f"❌ 验证约束失败: {str(e)}")
            return False
            
    def column_letter_to_number(self, letter):
        """将列字母转换为数字"""
        if isinstance(letter, int):
            return letter
            
        letter = letter.upper()
        result = 0
        for char in letter:
            result = result * 26 + (ord(char) - ord('A') + 1)
        return result
        
    def log(self, message):
        """记录日志"""
        if self.progress_callback:
            self.progress_callback(0, "日志", message)
            
    def run_optimization(self):
        """运行优化"""
        try:
            self.is_running = True
            self.log("🚀 开始优化...")
            
            # 连接Excel
            if not self.connect_excel():
                return False
                
            # 读取渠道
            if not self.read_channels():
                return False
                
            # 计算基准比例
            if not self.calculate_baseline_ratios():
                return False
                
            # 计算基准KPI
            if not self.calculate_baseline_kpi():
                return False
                
            # 开始迭代优化
            self.run_iterations()
            
            return True
            
        except Exception as e:
            self.log(f"❌ 优化失败: {str(e)}")
            return False
        finally:
            self.is_running = False
            self.disconnect_excel()
            
    def run_iterations(self):
        """运行迭代优化"""
        try:
            iterations = self.optimization_config.get('iterations', 100)
            num_solutions = self.optimization_config.get('num_solutions', 5)
            total_budget = self.optimization_config.get('total_budget', 1000000)
            
            self.current_best_kpi = self.baseline_kpi
            self.current_best_solution = self.baseline_ratios.copy()
            self.all_solutions = []
            
            for iteration in range(iterations):
                if not self.is_running:
                    break
                    
                # 生成随机解
                ratios = self.generate_random_solution()
                
                # 计算预算分配
                budgets = {}
                for channel in self.channels:
                    budgets[channel] = total_budget * ratios[channel]
                    
                # 计算KPI
                kpi = self.write_budgets_and_calculate_kpi(budgets)
                
                # 更新最优解
                if kpi > self.current_best_kpi:
                    self.current_best_kpi = kpi
                    self.current_best_solution = ratios.copy()
                    
                    # 实时更新监控界面
                    improvement = ((kpi - self.baseline_kpi) / self.baseline_kpi * 100) if self.baseline_kpi > 0 else 0
                    
                    if self.progress_callback:
                        self.progress_callback(
                            int((iteration + 1) / iterations * 90) + 10,
                            "优化进行中",
                            f"当前最优KPI: {kpi:,.2f}, 提升: {improvement:.1f}%, 迭代: {iteration + 1}/{iterations}"
                        )
                        
                # 保存解决方案
                solution = {
                    'ratios': ratios.copy(),
                    'budgets': budgets.copy(),
                    'kpi': kpi,
                    'iteration': iteration + 1
                }
                self.all_solutions.append(solution)
                
                # 短暂延迟以显示进度
                time.sleep(0.01)
                
            # 排序并选择最优解
            self.all_solutions.sort(key=lambda x: x['kpi'], reverse=True)
            self.all_solutions = self.all_solutions[:num_solutions]
            
            self.log(f"✅ 优化完成，生成 {len(self.all_solutions)} 个解决方案")
            
            # 最终更新监控界面
            if self.progress_callback:
                improvement = ((self.current_best_kpi - self.baseline_kpi) / self.baseline_kpi * 100) if self.baseline_kpi > 0 else 0
                self.progress_callback(
                    100,
                    "优化完成",
                    f"最优KPI: {self.current_best_kpi:,.2f}, 总提升: {improvement:.1f}%"
                )
                
        except Exception as e:
            self.log(f"❌ 迭代优化失败: {str(e)}")
            
    def get_results(self):
        """获取优化结果"""
        return {
            'baseline_kpi': self.baseline_kpi,
            'best_kpi': self.current_best_kpi,
            'solutions': self.all_solutions,
            'channels': self.channels,
            'baseline_ratios': self.baseline_ratios
        }
