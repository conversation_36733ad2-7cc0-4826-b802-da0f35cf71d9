@echo off
title Media Budget Optimizer v3.0

echo.
echo ========================================
echo   Media Budget Optimizer v3.0
echo ========================================
echo.
echo Starting v3.0 with all new features...
echo.

REM Check Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python not found!
    echo.
    echo Please install Python 3.7+ from:
    echo https://www.python.org/downloads/
    echo.
    echo Make sure to check "Add Python to PATH" during installation.
    echo.
    pause
    exit /b 1
)

echo Python environment: OK
echo.

REM Check dependencies
echo Checking dependencies...
python -c "import win32com.client" >nul 2>&1
if %errorlevel% neq 0 (
    echo Installing pywin32...
    python -m pip install pywin32
    if %errorlevel% neq 0 (
        echo Failed to install dependencies!
        echo Please run manually: pip install pywin32
        pause
        exit /b 1
    )
)

echo Dependencies: OK
echo.

REM Start the program
echo Starting Media Budget Optimizer v3.0...
echo.

python universal_media_optimizer_v3.py

if %errorlevel% neq 0 (
    echo.
    echo Program failed to start!
    echo Error code: %errorlevel%
    echo.
    echo Try:
    echo 1. Run as administrator
    echo 2. Check all files are present
    echo 3. Use the backup exe file
    echo.
    pause
)

echo.
echo Program exited normally.
pause
