#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
媒体预算优化工具 v4.1 - Mac版本
改进版：16:9布局，完整功能实现，跨平台兼容

主要改进：
1. 16:9比例布局，合理分布模块
2. 数据范围配置包含预算读取列和KPI读取配置
3. 输出配置简化为文件名和路径
4. 统一滚动界面，无左右分栏
5. 完整的渠道管理和优化功能
6. Mac平台兼容性（使用openpyxl替代win32com）
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
from datetime import datetime
import traceback
import os
import random
import time

# Mac版本使用openpyxl替代win32com
try:
    import openpyxl
    from openpyxl.utils import get_column_letter, column_index_from_string
    EXCEL_BACKEND = "openpyxl"
    print("✅ 使用openpyxl作为Excel后端")
except ImportError:
    print("❌ 请安装openpyxl: pip install openpyxl")
    EXCEL_BACKEND = None

class MediaOptimizerV41Mac:
    """媒体预算优化工具v4.1 Mac版本"""
    
    def __init__(self):
        """初始化程序"""
        print("初始化媒体预算优化工具 v4.1 Mac版...")
        
        try:
            # 检查Excel后端
            if EXCEL_BACKEND is None:
                messagebox.showerror("错误", "缺少必要依赖：openpyxl\n请运行：pip install openpyxl")
                return
                
            # 创建主窗口
            self.root = tk.Tk()
            self.root.title("媒体预算优化工具 v4.1 - Mac版")
            self.root.geometry("1600x900")  # 16:9比例
            self.root.minsize(1200, 700)
            
            # 设置颜色主题
            self.setup_colors()
            
            # 设置变量
            self.setup_variables()
            
            # 创建界面
            self.create_unified_scrollable_layout()
            
            # 绑定事件
            self.root.bind("<Configure>", self.on_window_resize)
            
            print("✅ 创建主窗口成功")
            print("✅ 程序初始化完成！")
            
        except Exception as e:
            print(f"❌ 初始化失败: {e}")
            traceback.print_exc()
            raise
            
    def setup_colors(self):
        """设置颜色主题"""
        self.colors = {
            'background': '#F8F9FA',
            'surface': '#FFFFFF',
            'primary': '#2E86AB',
            'secondary': '#A23B72',
            'accent': '#F18F01',
            'text': '#2C3E50',
            'text_light': '#7F8C8D',
            'success': '#27AE60',
            'warning': '#F39C12',
            'error': '#E74C3C'
        }
        
        # 设置根窗口背景
        self.root.configure(bg=self.colors['background'])
        
    def setup_variables(self):
        """设置变量"""
        try:
            # 文件配置
            self.file_path_var = tk.StringVar()
            self.sheet_name_var = tk.StringVar()
            
            # 数据范围配置 - 参考v3.0版本
            self.data_start_row_var = tk.StringVar()
            self.data_end_row_var = tk.StringVar()
            self.data_start_col_var = tk.StringVar()
            self.data_end_col_var = tk.StringVar()
            self.channel_row_var = tk.StringVar()
            
            # 预算读取配置 - 新增，读取预算的列
            self.budget_col_var = tk.StringVar()
            
            # KPI读取配置 - 新增
            self.kpi_start_row_var = tk.StringVar()
            self.kpi_end_row_var = tk.StringVar()
            self.kpi_col_var = tk.StringVar()
            
            # 输出文件配置 - 简化为文件名和路径
            self.output_filename_var = tk.StringVar(value="optimization_results.xlsx")  # 默认文件名
            self.output_path_var = tk.StringVar()
            
            # 基准比例设置
            self.baseline_method_var = tk.StringVar(value="manual")
            self.baseline_start_row_var = tk.StringVar()
            self.baseline_end_row_var = tk.StringVar()
            self.baseline_start_col_var = tk.StringVar()
            self.baseline_end_col_var = tk.StringVar()
            
            # 优化参数
            self.total_budget_var = tk.StringVar()
            self.num_solutions_var = tk.StringVar(value="5")
            self.iterations_var = tk.StringVar(value="100")
            self.kpi_type_var = tk.StringVar(value="Non-BHT")
            
            # 实时监控
            self.baseline_kpi_var = tk.StringVar(value="0")
            self.current_best_kpi_var = tk.StringVar(value="0")
            self.improvement_var = tk.StringVar(value="0%")
            self.progress_var = tk.StringVar(value="0%")
            self.status_var = tk.StringVar(value="就绪")
            
            # 数据存储
            self.channels_data = []
            self.channel_types = {}
            self.optimization_running = False
            
        except Exception as e:
            print(f"❌ 变量设置失败: {e}")
            raise
            
    def create_unified_scrollable_layout(self):
        """创建16:9比例的统一滚动布局"""
        try:
            # 主容器 - 使用pack布局，确保完全填充
            self.main_frame = tk.Frame(self.root, bg=self.colors['background'])
            self.main_frame.pack(fill='both', expand=True, padx=10, pady=10)
            
            # 创建Canvas和Scrollbar实现统一滚动
            self.main_canvas = tk.Canvas(self.main_frame, bg=self.colors['background'], highlightthickness=0)
            self.main_scrollbar = ttk.Scrollbar(self.main_frame, orient="vertical", command=self.main_canvas.yview)
            self.scrollable_frame = tk.Frame(self.main_canvas, bg=self.colors['background'])
            
            # 配置滚动
            self.scrollable_frame.bind(
                "<Configure>",
                lambda event: self.main_canvas.configure(scrollregion=self.main_canvas.bbox("all"))
            )
            
            self.main_canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
            self.main_canvas.configure(yscrollcommand=self.main_scrollbar.set)
            
            # 布局Canvas和Scrollbar - 确保完全填充
            self.main_canvas.pack(side="left", fill="both", expand=True)
            self.main_scrollbar.pack(side="right", fill="y")
            
            # 创建16:9比例的网格布局
            self.create_grid_layout()
            
            # 绑定鼠标滚轮
            self.bind_mousewheel(self.main_canvas)
            
        except Exception as e:
            print(f"❌ 布局创建失败: {e}")
            self.create_simple_layout()
            
    def create_grid_layout(self):
        """创建16:9比例的网格布局"""
        try:
            # 配置scrollable_frame的网格权重 - 16:9比例
            # 16列，9行的网格系统
            for i in range(16):
                self.scrollable_frame.grid_columnconfigure(i, weight=1)
            for i in range(9):
                self.scrollable_frame.grid_rowconfigure(i, weight=1)
            
            # 第一行：文件配置和数据范围 (占用16列)
            self.create_file_and_data_config_section()
            
            # 第二行：基准比例和优化配置 (各占8列)
            self.create_baseline_and_optimization_section()
            
            # 第三行：渠道管理 (占用16列)
            self.create_channel_management_section()
            
            # 第四行：实时监控 (占用16列)
            self.create_monitoring_section()
            
            # 第五到第八行：结果显示 (占用16列，4行)
            self.create_results_section()
            
            # 第九行：日志 (占用16列)
            self.create_log_section()
            
        except Exception as e:
            print(f"❌ 网格布局创建失败: {e}")
            
    def create_simple_layout(self):
        """创建简单布局作为备用"""
        try:
            # 简单的垂直布局
            main_frame = tk.Frame(self.root, bg=self.colors['background'])
            main_frame.pack(fill='both', expand=True, padx=20, pady=20)
            
            # 标题
            title_label = tk.Label(main_frame, 
                                 text="媒体预算优化工具 v4.1 - Mac版", 
                                 bg=self.colors['background'], 
                                 fg=self.colors['primary'],
                                 font=('Arial', 16, 'bold'))
            title_label.pack(pady=20)
            
            # 状态信息
            status_label = tk.Label(main_frame, 
                                  text="程序已启动，但界面创建遇到问题。\n请检查系统兼容性。", 
                                  bg=self.colors['background'], 
                                  fg=self.colors['text'],
                                  font=('Arial', 12))
            status_label.pack(pady=20)
            
        except Exception as e:
            print(f"❌ 简单布局创建失败: {e}")
            
    def bind_mousewheel(self, widget):
        """绑定鼠标滚轮事件"""
        try:
            def _on_mousewheel(event):
                # Mac使用不同的滚轮事件
                if hasattr(event, 'delta'):
                    delta = event.delta
                else:
                    delta = event.num
                    
                self.main_canvas.yview_scroll(int(-1 * (delta / 120)), "units")
                
            # Mac平台的滚轮绑定
            widget.bind("<MouseWheel>", _on_mousewheel)  # Windows/Linux
            widget.bind("<Button-4>", lambda e: self.main_canvas.yview_scroll(-1, "units"))  # Linux
            widget.bind("<Button-5>", lambda e: self.main_canvas.yview_scroll(1, "units"))   # Linux
            
            # 递归绑定子组件
            for child in widget.winfo_children():
                self.bind_mousewheel(child)
                
        except Exception as e:
            print(f"鼠标滚轮绑定失败: {e}")
            
    def add_log(self, message):
        """添加日志"""
        try:
            timestamp = datetime.now().strftime("%H:%M:%S")
            log_message = f"[{timestamp}] {message}\n"
            
            if hasattr(self, 'log_text'):
                self.log_text.insert(tk.END, log_message)
                self.log_text.see(tk.END)
            else:
                print(log_message.strip())
                
        except Exception as e:
            print(f"日志添加失败: {e}")

    def create_file_and_data_config_section(self):
        """创建文件配置和数据范围区域 - 第一行"""
        try:
            # 文件配置卡片 (占用前8列)
            file_card = tk.Frame(self.scrollable_frame, bg=self.colors['surface'], relief='solid', bd=1)
            file_card.grid(row=0, column=0, columnspan=8, sticky='nsew', padx=(0, 5), pady=(0, 10))

            # 文件配置标题
            title_frame = tk.Frame(file_card, bg=self.colors['surface'])
            title_frame.pack(fill='x', padx=10, pady=(10, 5))

            tk.Label(title_frame, text="📁 文件配置",
                    bg=self.colors['surface'], fg=self.colors['primary'],
                    font=('Arial', 11, 'bold')).pack(anchor='w')

            # 文件选择
            file_frame = tk.Frame(file_card, bg=self.colors['surface'])
            file_frame.pack(fill='x', padx=10, pady=5)

            tk.Label(file_frame, text="Excel文件:",
                    bg=self.colors['surface'], fg=self.colors['text'],
                    font=('Arial', 9)).pack(anchor='w')

            file_input_frame = tk.Frame(file_frame, bg=self.colors['surface'])
            file_input_frame.pack(fill='x', pady=(3, 0))

            self.file_entry = tk.Entry(file_input_frame, textvariable=self.file_path_var,
                                     state='readonly', font=('Arial', 8))
            self.file_entry.pack(side='left', fill='x', expand=True, padx=(0, 5))

            tk.Button(file_input_frame, text="浏览", command=self.browse_file,
                     font=('Arial', 8), bg=self.colors['primary'],
                     fg='white', relief='flat').pack(side='right')

            # 工作表选择
            sheet_frame = tk.Frame(file_card, bg=self.colors['surface'])
            sheet_frame.pack(fill='x', padx=10, pady=(5, 10))

            tk.Label(sheet_frame, text="工作表:",
                    bg=self.colors['surface'], fg=self.colors['text'],
                    font=('Arial', 9)).pack(anchor='w')

            self.sheet_combo = ttk.Combobox(sheet_frame, textvariable=self.sheet_name_var,
                                          state='readonly', font=('Arial', 8))
            self.sheet_combo.pack(fill='x', pady=(3, 0))

            # 数据范围配置卡片 (占用后8列)
            data_card = tk.Frame(self.scrollable_frame, bg=self.colors['surface'], relief='solid', bd=1)
            data_card.grid(row=0, column=8, columnspan=8, sticky='nsew', padx=(5, 0), pady=(0, 10))

            # 数据范围标题
            data_title_frame = tk.Frame(data_card, bg=self.colors['surface'])
            data_title_frame.pack(fill='x', padx=10, pady=(10, 5))

            tk.Label(data_title_frame, text="📊 数据范围配置",
                    bg=self.colors['surface'], fg=self.colors['primary'],
                    font=('Arial', 11, 'bold')).pack(anchor='w')

            # 数据行范围
            row_frame = tk.Frame(data_card, bg=self.colors['surface'])
            row_frame.pack(fill='x', padx=10, pady=3)

            tk.Label(row_frame, text="数据行范围:",
                    bg=self.colors['surface'], fg=self.colors['text'],
                    font=('Arial', 9)).pack(anchor='w')

            row_input_frame = tk.Frame(row_frame, bg=self.colors['surface'])
            row_input_frame.pack(fill='x', pady=(2, 0))

            tk.Entry(row_input_frame, textvariable=self.data_start_row_var,
                    font=('Arial', 8), width=6).pack(side='left', padx=(0, 3))
            tk.Label(row_input_frame, text="到", bg=self.colors['surface'],
                    font=('Arial', 8)).pack(side='left', padx=2)
            tk.Entry(row_input_frame, textvariable=self.data_end_row_var,
                    font=('Arial', 8), width=6).pack(side='left', padx=(2, 0))

            # 数据列范围
            col_frame = tk.Frame(data_card, bg=self.colors['surface'])
            col_frame.pack(fill='x', padx=10, pady=3)

            tk.Label(col_frame, text="数据列范围:",
                    bg=self.colors['surface'], fg=self.colors['text'],
                    font=('Arial', 9)).pack(anchor='w')

            col_input_frame = tk.Frame(col_frame, bg=self.colors['surface'])
            col_input_frame.pack(fill='x', pady=(2, 0))

            tk.Entry(col_input_frame, textvariable=self.data_start_col_var,
                    font=('Arial', 8), width=6).pack(side='left', padx=(0, 3))
            tk.Label(col_input_frame, text="到", bg=self.colors['surface'],
                    font=('Arial', 8)).pack(side='left', padx=2)
            tk.Entry(col_input_frame, textvariable=self.data_end_col_var,
                    font=('Arial', 8), width=6).pack(side='left', padx=(2, 0))

            # 渠道名称行和预算列
            misc_frame = tk.Frame(data_card, bg=self.colors['surface'])
            misc_frame.pack(fill='x', padx=10, pady=(3, 5))

            # 渠道名称行
            channel_row_frame = tk.Frame(misc_frame, bg=self.colors['surface'])
            channel_row_frame.pack(fill='x', pady=2)

            tk.Label(channel_row_frame, text="渠道名称行:",
                    bg=self.colors['surface'], fg=self.colors['text'],
                    font=('Arial', 9)).pack(side='left')
            tk.Entry(channel_row_frame, textvariable=self.channel_row_var,
                    font=('Arial', 8), width=6).pack(side='left', padx=(5, 0))

            # 预算读取列
            budget_col_frame = tk.Frame(misc_frame, bg=self.colors['surface'])
            budget_col_frame.pack(fill='x', pady=2)

            tk.Label(budget_col_frame, text="预算读取列:",
                    bg=self.colors['surface'], fg=self.colors['text'],
                    font=('Arial', 9)).pack(side='left')
            tk.Entry(budget_col_frame, textvariable=self.budget_col_var,
                    font=('Arial', 8), width=6).pack(side='left', padx=(5, 0))

            # KPI读取配置
            kpi_frame = tk.Frame(data_card, bg=self.colors['surface'])
            kpi_frame.pack(fill='x', padx=10, pady=(3, 10))

            tk.Label(kpi_frame, text="KPI读取配置:",
                    bg=self.colors['surface'], fg=self.colors['text'],
                    font=('Arial', 10, 'bold')).pack(anchor='w', pady=(0, 3))

            # KPI行范围
            kpi_row_frame = tk.Frame(kpi_frame, bg=self.colors['surface'])
            kpi_row_frame.pack(fill='x', pady=2)

            tk.Label(kpi_row_frame, text="KPI行范围:",
                    bg=self.colors['surface'], fg=self.colors['text'],
                    font=('Arial', 9)).pack(anchor='w')

            kpi_row_input_frame = tk.Frame(kpi_row_frame, bg=self.colors['surface'])
            kpi_row_input_frame.pack(fill='x', pady=(2, 0))

            tk.Entry(kpi_row_input_frame, textvariable=self.kpi_start_row_var,
                    font=('Arial', 8), width=6).pack(side='left', padx=(0, 3))
            tk.Label(kpi_row_input_frame, text="到", bg=self.colors['surface'],
                    font=('Arial', 8)).pack(side='left', padx=2)
            tk.Entry(kpi_row_input_frame, textvariable=self.kpi_end_row_var,
                    font=('Arial', 8), width=6).pack(side='left', padx=(2, 0))

            # KPI列
            kpi_col_frame = tk.Frame(kpi_frame, bg=self.colors['surface'])
            kpi_col_frame.pack(fill='x', pady=2)

            tk.Label(kpi_col_frame, text="KPI读取列:",
                    bg=self.colors['surface'], fg=self.colors['text'],
                    font=('Arial', 9)).pack(side='left')
            tk.Entry(kpi_col_frame, textvariable=self.kpi_col_var,
                    font=('Arial', 8), width=6).pack(side='left', padx=(5, 0))

        except Exception as e:
            print(f"❌ 文件和数据配置区域创建失败: {e}")

    def browse_file(self):
        """浏览文件"""
        try:
            file_path = filedialog.askopenfilename(
                title="选择Excel文件",
                filetypes=[("Excel文件", "*.xlsx *.xls"), ("所有文件", "*.*")]
            )

            if file_path:
                self.file_path_var.set(file_path)
                self.add_log(f"选择文件: {file_path}")

                # 读取工作表列表
                self.load_worksheets()

        except Exception as e:
            self.add_log(f"文件选择失败: {e}")

    def load_worksheets(self):
        """加载工作表列表"""
        try:
            if not self.file_path_var.get():
                return

            # 使用openpyxl读取工作表
            workbook = openpyxl.load_workbook(self.file_path_var.get(), read_only=True)
            sheet_names = workbook.sheetnames
            self.sheet_combo['values'] = sheet_names

            if sheet_names:
                self.sheet_name_var.set(sheet_names[0])
                self.add_log(f"✅ 读取到 {len(sheet_names)} 个工作表")
            else:
                self.add_log("⚠️ 未找到工作表")

            workbook.close()

        except Exception as e:
            self.add_log(f"读取工作表失败: {e}")

    def run(self):
        """运行程序"""
        try:
            print("启动主循环...")
            self.add_log("媒体预算优化工具 v4.1 Mac版已启动")
            self.root.mainloop()
        except Exception as e:
            print(f"❌ 程序运行失败: {e}")
            traceback.print_exc()

if __name__ == "__main__":
    try:
        print("开始启动媒体预算优化工具 v4.1 Mac版...")
        app = MediaOptimizerV41Mac()
        app.run()
    except Exception as e:
        print(f"❌ 程序启动失败: {e}")
        traceback.print_exc()
        input("按回车键退出...")
