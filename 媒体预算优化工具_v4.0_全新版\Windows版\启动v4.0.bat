@echo off
chcp 65001 >nul
title 媒体预算优化工具 v4.0 - 全新版

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║            媒体预算优化工具 v4.0 - 全新版                   ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🆕 v4.0 全新功能：
echo   ✨ 界面美化100倍，自适应窗口大小
echo   ⚖️ 基准比例数据表计算
echo   🔒 修复渠道约束问题
echo   📊 实时监控数据更新
echo   🏆 优化结果预览改进
echo   🎯 渠道类型编辑功能
echo.

REM 检查Python环境
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未检测到Python环境！
    echo.
    echo 💡 解决方案：
    echo 1. 安装Python 3.7+：https://www.python.org/downloads/
    echo 2. 安装时勾选 "Add Python to PATH"
    echo 3. 重新打开命令行窗口
    echo.
    pause
    exit /b 1
)

echo ✅ Python环境检测通过
echo.

REM 检查依赖
echo 🔍 检查必要依赖...
python -c "import win32com.client" >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️ 缺少pywin32库，正在自动安装...
    echo 📦 安装中，请稍候...
    python -m pip install pywin32
    if %errorlevel% neq 0 (
        echo ❌ 依赖安装失败！
        echo 💡 请手动运行：pip install pywin32
        echo 或者联系技术支持
        pause
        exit /b 1
    )
    echo ✅ pywin32安装成功
)

echo ✅ 依赖检查完成
echo.

REM 启动v4.0程序
echo 🚀 启动媒体预算优化工具 v4.0...
echo 📊 加载全新界面中...
echo.

REM 记录Python进程ID以便后续关闭
for /f "tokens=2" %%i in ('tasklist /fi "imagename eq python.exe" /fo table /nh 2^>nul ^| find /c "python.exe"') do set PYTHON_COUNT_BEFORE=%%i

REM 启动Python程序
python universal_media_optimizer_v4.py

REM 程序退出后的处理
echo.
echo 🔄 程序已退出，正在清理...

REM 查找并关闭可能残留的Python进程
for /f "tokens=2" %%i in ('tasklist /fi "imagename eq python.exe" /fo table /nh 2^>nul ^| find /c "python.exe"') do set PYTHON_COUNT_AFTER=%%i

REM 如果Python进程数量增加了，说明有新的Python进程，尝试关闭
if %PYTHON_COUNT_AFTER% GTR %PYTHON_COUNT_BEFORE% (
    echo 🔄 检测到Python进程，正在关闭...
    
    REM 尝试优雅关闭
    taskkill /f /im python.exe /fi "WINDOWTITLE eq 媒体预算优化工具*" >nul 2>&1
    
    REM 等待一下
    timeout /t 2 /nobreak >nul
    
    REM 检查是否还有相关进程
    tasklist /fi "imagename eq python.exe" /fi "WINDOWTITLE eq 媒体预算优化工具*" >nul 2>&1
    if %errorlevel% equ 0 (
        echo ⚠️ 强制关闭残留的Python进程...
        taskkill /f /im python.exe /fi "WINDOWTITLE eq 媒体预算优化工具*" >nul 2>&1
    )
    
    echo ✅ Python进程已清理
)

echo.
echo ✅ 程序正常退出
echo 🎉 感谢使用媒体预算优化工具 v4.0！

REM 短暂暂停以显示消息
timeout /t 3 /nobreak >nul
