#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
媒体预算优化工具 v3.0 启动器
自动检查依赖并启动主程序
"""

import sys
import os
import subprocess
import tkinter as tk
from tkinter import messagebox

def check_dependencies():
    """检查必要的依赖"""
    missing_deps = []
    
    try:
        import win32com.client
    except ImportError:
        missing_deps.append("pywin32")
    
    try:
        import tkinter.ttk
    except ImportError:
        missing_deps.append("tkinter")
    
    return missing_deps

def install_dependencies(deps):
    """安装缺失的依赖"""
    for dep in deps:
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
            print(f"✅ 成功安装: {dep}")
        except:
            print(f"❌ 安装失败: {dep}")
            return False
    return True

def main():
    """主函数"""
    print("🚀 媒体预算优化工具 v3.0 启动器")
    print("=" * 50)
    
    # 检查主程序文件
    if not os.path.exists("universal_media_optimizer_v2.py"):
        messagebox.showerror("错误", "找不到主程序文件！\n请确保所有文件都在同一目录下。")
        return
    
    # 检查依赖
    missing_deps = check_dependencies()
    
    if missing_deps:
        print(f"⚠️ 缺少依赖: {', '.join(missing_deps)}")
        
        # 询问是否自动安装
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        result = messagebox.askyesno(
            "缺少依赖", 
            f"检测到缺少以下依赖库：\n{', '.join(missing_deps)}\n\n是否自动安装？"
        )
        
        if result:
            print("📦 正在安装依赖...")
            if install_dependencies(missing_deps):
                print("✅ 依赖安装完成")
            else:
                messagebox.showerror("错误", "依赖安装失败！\n请手动运行：pip install pywin32")
                return
        else:
            messagebox.showinfo("提示", "请手动安装依赖后再运行：\npip install pywin32")
            return
    
    # 启动主程序
    print("🎯 启动媒体预算优化工具 v3.0...")
    try:
        # 导入并运行主程序
        import universal_media_optimizer_v2
        print("✅ 程序启动成功！")
    except Exception as e:
        messagebox.showerror("启动失败", f"程序启动失败：\n{str(e)}")
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    main()
