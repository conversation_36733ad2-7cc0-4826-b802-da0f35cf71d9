#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
媒体预算优化工具 v4.0 - 修复版
包含错误处理和调试信息
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
from datetime import datetime
import re
import sys
import traceback

class MediaOptimizerV4Fixed:
    def __init__(self):
        try:
            print("初始化程序...")
            self.root = tk.Tk()
            print("✅ 创建主窗口成功")
            
            self.setup_window()
            print("✅ 窗口设置完成")
            
            self.setup_styles()
            print("✅ 样式设置完成")
            
            self.setup_variables()
            print("✅ 变量设置完成")
            
            self.create_widgets()
            print("✅ 界面组件创建完成")
            
            self.setup_layout()
            print("✅ 布局设置完成")
            
            self.setup_bindings()
            print("✅ 事件绑定完成")
            
            # 数据存储
            self.channels_data = []
            self.channel_types = {}
            self.optimization_running = False
            self.current_best_kpi = 0
            self.baseline_kpi = 0
            
            print("✅ 程序初始化完成！")
            
        except Exception as e:
            print(f"❌ 初始化失败: {e}")
            traceback.print_exc()
            raise
        
    def setup_window(self):
        """设置主窗口"""
        try:
            self.root.title("媒体预算优化工具 v4.0 - 修复版")
            self.root.geometry("1600x1000")
            self.root.minsize(1200, 800)
            
            # 设置窗口样式
            self.root.configure(bg='#f0f0f0')
            
            # 窗口居中
            self.center_window()
            
            # 设置关闭事件
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            
        except Exception as e:
            print(f"❌ 窗口设置失败: {e}")
            raise
        
    def center_window(self):
        """窗口居中显示"""
        try:
            self.root.update_idletasks()
            width = self.root.winfo_width()
            height = self.root.winfo_height()
            x = (self.root.winfo_screenwidth() // 2) - (width // 2)
            y = (self.root.winfo_screenheight() // 2) - (height // 2)
            self.root.geometry(f'{width}x{height}+{x}+{y}')
        except Exception as e:
            print(f"⚠️ 窗口居中失败: {e}")
        
    def setup_styles(self):
        """设置美化样式"""
        try:
            self.style = ttk.Style()
            self.style.theme_use('clam')
            
            # 定义颜色主题
            self.colors = {
                'primary': '#2E86AB',
                'secondary': '#A23B72',
                'accent': '#F18F01',
                'success': '#C73E1D',
                'background': '#F5F5F5',
                'surface': '#FFFFFF',
                'text': '#2C3E50',
                'text_light': '#7F8C8D',
                'border': '#BDC3C7'
            }
            
            # 配置基本样式
            self.style.configure('Main.TFrame', 
                               background=self.colors['background'],
                               relief='flat')
            
            self.style.configure('Card.TFrame',
                               background=self.colors['surface'],
                               relief='solid',
                               borderwidth=1)
            
            self.style.configure('Title.TLabel',
                               background=self.colors['surface'],
                               foreground=self.colors['primary'],
                               font=('Microsoft YaHei UI', 14, 'bold'))
            
            self.style.configure('Normal.TLabel',
                               background=self.colors['surface'],
                               foreground=self.colors['text'],
                               font=('Microsoft YaHei UI', 9))
            
        except Exception as e:
            print(f"❌ 样式设置失败: {e}")
            raise
        
    def setup_variables(self):
        """设置变量"""
        try:
            # 文件路径
            self.file_path_var = tk.StringVar()
            self.sheet_name_var = tk.StringVar()
            
            # 数据范围
            self.data_start_row_var = tk.StringVar()
            self.data_end_row_var = tk.StringVar()
            self.data_start_col_var = tk.StringVar()
            self.data_end_col_var = tk.StringVar()
            self.channel_row_var = tk.StringVar()
            
            # 优化参数
            self.total_budget_var = tk.StringVar()
            self.kpi_type_var = tk.StringVar(value="Non-BHT")
            
            # 实时监控
            self.status_var = tk.StringVar(value="就绪")
            
        except Exception as e:
            print(f"❌ 变量设置失败: {e}")
            raise
        
    def create_widgets(self):
        """创建所有界面组件"""
        try:
            # 主容器
            self.main_container = ttk.Frame(self.root, style='Main.TFrame')
            self.main_container.pack(fill='both', expand=True, padx=10, pady=10)
            
            # 创建简化的界面
            self.create_simple_interface()
            
        except Exception as e:
            print(f"❌ 组件创建失败: {e}")
            raise
            
    def create_simple_interface(self):
        """创建简化界面"""
        try:
            # 标题
            title_label = ttk.Label(self.main_container, 
                                   text="媒体预算优化工具 v4.0 - 修复版", 
                                   style='Title.TLabel')
            title_label.pack(pady=20)
            
            # 状态显示
            status_frame = ttk.Frame(self.main_container, style='Card.TFrame')
            status_frame.pack(fill='x', padx=20, pady=10)
            
            ttk.Label(status_frame, text="状态:", style='Normal.TLabel').pack(side='left', padx=10, pady=10)
            ttk.Label(status_frame, textvariable=self.status_var, style='Normal.TLabel').pack(side='left', padx=10, pady=10)
            
            # 文件选择
            file_frame = ttk.Frame(self.main_container, style='Card.TFrame')
            file_frame.pack(fill='x', padx=20, pady=10)
            
            ttk.Label(file_frame, text="Excel文件:", style='Normal.TLabel').pack(anchor='w', padx=10, pady=(10, 5))
            
            file_input_frame = ttk.Frame(file_frame, style='Card.TFrame')
            file_input_frame.pack(fill='x', padx=10, pady=(0, 10))
            
            self.file_entry = ttk.Entry(file_input_frame, textvariable=self.file_path_var, state='readonly')
            self.file_entry.pack(side='left', fill='x', expand=True, padx=(0, 10))
            
            ttk.Button(file_input_frame, text="浏览", command=self.browse_file).pack(side='right')
            
            # 测试按钮
            test_frame = ttk.Frame(self.main_container, style='Card.TFrame')
            test_frame.pack(fill='x', padx=20, pady=10)
            
            ttk.Button(test_frame, text="测试功能", command=self.test_function).pack(pady=20)
            
            # 日志区域
            log_frame = ttk.Frame(self.main_container, style='Card.TFrame')
            log_frame.pack(fill='both', expand=True, padx=20, pady=10)
            
            ttk.Label(log_frame, text="运行日志:", style='Normal.TLabel').pack(anchor='w', padx=10, pady=(10, 5))
            
            self.log_text = scrolledtext.ScrolledText(log_frame, height=10, wrap=tk.WORD)
            self.log_text.pack(fill='both', expand=True, padx=10, pady=(0, 10))
            
            self.add_log("程序启动成功！")
            
        except Exception as e:
            print(f"❌ 简化界面创建失败: {e}")
            raise
            
    def setup_layout(self):
        """设置布局"""
        try:
            # 简化版本不需要复杂布局
            pass
        except Exception as e:
            print(f"❌ 布局设置失败: {e}")
            
    def setup_bindings(self):
        """设置事件绑定"""
        try:
            # 简化版本的基本绑定
            pass
        except Exception as e:
            print(f"❌ 事件绑定失败: {e}")
            
    def browse_file(self):
        """浏览文件"""
        try:
            filename = filedialog.askopenfilename(
                title="选择Excel文件",
                filetypes=[("Excel files", "*.xlsx *.xls")]
            )
            if filename:
                self.file_path_var.set(filename)
                self.add_log(f"选择文件: {filename}")
        except Exception as e:
            self.add_log(f"文件选择失败: {e}")
            
    def test_function(self):
        """测试功能"""
        try:
            self.add_log("测试功能运行中...")
            self.status_var.set("测试中")
            
            # 模拟一些操作
            self.root.after(1000, lambda: self.status_var.set("测试完成"))
            self.root.after(1000, lambda: self.add_log("测试功能完成！"))
            
        except Exception as e:
            self.add_log(f"测试功能失败: {e}")
            
    def add_log(self, message):
        """添加日志"""
        try:
            timestamp = datetime.now().strftime("%H:%M:%S")
            log_message = f"[{timestamp}] {message}\n"
            
            if hasattr(self, 'log_text'):
                self.log_text.insert(tk.END, log_message)
                self.log_text.see(tk.END)
                self.root.update_idletasks()
            else:
                print(log_message.strip())
                
        except Exception as e:
            print(f"日志添加失败: {e}")
            
    def on_closing(self):
        """窗口关闭时的处理"""
        try:
            self.add_log("程序正在关闭...")
            self.root.destroy()
        except Exception as e:
            print(f"关闭处理失败: {e}")
            self.root.destroy()
            
    def run(self):
        """运行应用"""
        try:
            print("启动主循环...")
            self.root.mainloop()
            print("程序正常退出")
        except Exception as e:
            print(f"运行失败: {e}")
            traceback.print_exc()

def main():
    """主函数"""
    try:
        print("开始启动媒体预算优化工具 v4.0 修复版...")
        app = MediaOptimizerV4Fixed()
        app.run()
    except Exception as e:
        print(f"程序启动失败: {e}")
        traceback.print_exc()
        input("按回车键退出...")

if __name__ == "__main__":
    main()
