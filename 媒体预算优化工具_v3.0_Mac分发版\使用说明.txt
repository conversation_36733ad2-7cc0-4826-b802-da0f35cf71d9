# 媒体预算优化工具 v3.0 Mac版 - 分发版

## 🍎 欢迎使用Mac跨平台版！

这是专为Mac用户开发的媒体预算优化工具v3.0版本，支持macOS、Linux和Windows系统。

## 🚀 快速启动

### 方法1: 一键启动（推荐）
```bash
# 在终端中进入程序目录
cd /path/to/媒体预算优化工具_v3.0_Mac分发版

# 给予执行权限
chmod +x 启动v3.0.sh

# 运行启动脚本
./启动v3.0.sh
```

### 方法2: 手动启动
```bash
# 安装依赖
python3 install_mac_dependencies.py

# 启动程序
python3 universal_media_optimizer_mac.py
```

### 方法3: 直接运行
```bash
# 安装依赖
pip3 install openpyxl pandas numpy

# 启动程序
python3 universal_media_optimizer_mac.py
```

## 🆕 v3.0 Mac版功能

### ✅ 已实现功能
- **16:9屏幕优化布局**: 左右分栏设计，适配宽屏显示器
- **实时监控仪表板**: 基准KPI、最优KPI、状态监控等
- **KPI类型选择**: 支持BHT和Non-BHT两种计算方式
- **Excel文件读取**: 使用openpyxl库，跨平台兼容
- **列字母输入**: Excel风格的A、B、C输入方式
- **跨平台兼容**: 支持macOS、Linux、Windows系统

### 🚧 开发中功能
- 完整的优化算法（Windows版本功能完整）
- 滑块配比设置（当前为简化版）
- 美化Excel输出（开发中）
- 高级约束设置（简化版）

## 💻 系统要求

- **macOS**: 10.12 Sierra 或更高版本
- **Linux**: Ubuntu 18.04+ 或其他现代发行版
- **Windows**: 10/11 (作为备用)
- **Python**: 3.7 或更高版本
- **内存**: 4GB 或更多
- **存储**: 100MB 可用空间

## 🔧 安装依赖

### macOS系统
```bash
# 使用Homebrew安装Python（如果没有）
brew install python3

# 安装依赖
pip3 install openpyxl pandas numpy
```

### Ubuntu/Debian系统
```bash
# 安装Python和pip
sudo apt update
sudo apt install python3 python3-pip python3-tk

# 安装依赖
pip3 install openpyxl pandas numpy
```

### Windows系统
```bash
# 安装依赖
pip install openpyxl pandas numpy
```

## 📁 文件说明

- **启动v3.0.sh** - Mac/Linux启动脚本
- **universal_media_optimizer_mac.py** - Mac版主程序
- **install_mac_dependencies.py** - 依赖安装脚本
- **Gatorade simulation tool_cal.xlsx** - 示例Excel文件
- **使用说明.txt** - 本文件

## 💡 使用技巧

### 首次使用
1. 确保Python 3.7+已安装
2. 运行启动脚本或手动安装依赖
3. 使用示例Excel文件测试功能

### 数据准备
1. 使用.xlsx格式的Excel文件（不支持.xls）
2. 确保文件路径正确，无中文路径问题
3. 验证工作表名称存在

### 界面操作
1. 左侧面板：配置参数
2. 右侧面板：监控和日志
3. 支持鼠标滚轮滚动（跨平台）
4. 窗口可调整大小

## ⚠️ 注意事项

### 当前状态
- Mac版本为预览版，界面功能完整
- 核心优化算法仍在开发中
- 如需完整功能，建议使用Windows版本

### 文件格式
- 仅支持.xlsx格式的Excel文件
- 不支持.xls格式（旧版Excel）
- 建议文件路径不包含特殊字符

### 权限问题
- 确保对Excel文件有读取权限
- 确保对输出目录有写入权限
- macOS可能需要授权访问文件

## 🔧 故障排除

### 问题1: 提示缺少openpyxl库
```bash
# 解决方案
pip3 install openpyxl
```

### 问题2: Python版本过低
```bash
# 检查Python版本
python3 --version

# 如果版本低于3.7，请升级Python
```

### 问题3: 无法读取Excel文件
- 检查文件路径是否正确
- 确认工作表名称是否存在
- 验证文件格式为.xlsx
- 确保文件未被其他程序占用

### 问题4: 界面显示异常
- 确保使用Python 3.7+
- 检查tkinter是否正确安装
- 尝试调整窗口大小

### 问题5: macOS安全限制
```bash
# 如果提示安全限制，允许任何来源的应用
sudo spctl --master-disable

# 或者给予执行权限
chmod +x 启动v3.0.sh
```

### 问题6: 权限被拒绝
```bash
# 给予脚本执行权限
chmod +x 启动v3.0.sh

# 或者直接运行Python
python3 universal_media_optimizer_mac.py
```

## 🔄 版本对比

| 功能特性 | Windows版 | Mac版 |
|----------|-----------|-------|
| 基本界面 | ✅ 完整 | ✅ 完整 |
| Excel读取 | ✅ win32com | ✅ openpyxl |
| 优化算法 | ✅ 完整 | 🚧 开发中 |
| 实时监控 | ✅ 完整 | ✅ 完整 |
| 滑块配比 | ✅ 完整 | 🚧 简化版 |
| Excel输出 | ✅ 美化版 | 🚧 开发中 |
| 跨平台 | ❌ 仅Windows | ✅ 多平台 |

## 🚀 未来计划

- [ ] 完整优化算法移植
- [ ] 滑块配比设置完善
- [ ] 美化Excel输出功能
- [ ] 更多预设配比模板
- [ ] 数据可视化图表
- [ ] 打包为.app应用

## 📞 技术支持

如遇问题，请提供以下信息：
- 操作系统版本 (macOS/Linux/Windows)
- Python版本
- 错误信息截图
- Excel文件格式

## 🔗 相关链接

- **Python官网**: https://www.python.org/
- **openpyxl文档**: https://openpyxl.readthedocs.io/
- **Homebrew (macOS)**: https://brew.sh/

---

**版本**: v3.0 Mac分发版  
**更新日期**: 2025年6月22日  
**支持系统**: macOS, Linux, Windows  
**开发状态**: 预览版，持续开发中

🍎 感谢使用媒体预算优化工具Mac版！
