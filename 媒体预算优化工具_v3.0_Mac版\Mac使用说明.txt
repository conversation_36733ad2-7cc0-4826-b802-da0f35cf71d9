# 媒体预算优化工具 v3.0 Mac版 - 使用说明

## 🍎 欢迎使用Mac跨平台版！

这是专为Mac用户开发的跨平台版本，支持macOS、Linux和Windows系统。

## 📁 文件说明

- universal_media_optimizer_mac.py - Mac版主程序
- install_mac_dependencies.py - 依赖安装脚本
- Gatorade simulation tool_cal.xlsx - 示例Excel文件
- README_Mac版本.md - 详细功能说明
- Mac使用说明.txt - 本文件

## 🚀 安装和使用

### 方法1: 自动安装（推荐）

```bash
# 1. 打开终端，进入程序目录
cd /path/to/媒体预算优化工具_v3.0_Mac版

# 2. 安装依赖
python3 install_mac_dependencies.py

# 3. 运行程序
python3 universal_media_optimizer_mac.py
```

### 方法2: 手动安装

```bash
# 1. 安装Python依赖
pip3 install openpyxl pandas numpy

# 2. 运行程序
python3 universal_media_optimizer_mac.py
```

## 💻 系统要求

- **操作系统**: macOS 10.12+, Ubuntu 18.04+, Windows 10+
- **Python**: 3.7 或更高版本
- **内存**: 4GB 或更多
- **存储**: 100MB 可用空间

## 🆕 v3.0 Mac版功能

### ✅ 已实现功能
- 16:9屏幕优化布局
- 实时监控仪表板
- KPI类型选择（BHT/Non-BHT）
- Excel文件读取（使用openpyxl）
- 列字母输入支持
- 跨平台兼容性

### 🚧 开发中功能
- 完整的优化算法
- 滑块配比设置（简化版）
- 美化Excel输出
- 高级约束设置

## 🔧 安装依赖

### macOS系统

```bash
# 使用Homebrew安装Python（如果没有）
brew install python3

# 安装依赖
pip3 install openpyxl pandas numpy
```

### Ubuntu/Debian系统

```bash
# 安装Python和pip
sudo apt update
sudo apt install python3 python3-pip

# 安装依赖
pip3 install openpyxl pandas numpy
```

### Windows系统

```bash
# 安装依赖
pip install openpyxl pandas numpy
```

## 💡 使用技巧

1. **首次使用**：
   - 确保Python 3.7+已安装
   - 运行依赖安装脚本
   - 使用示例Excel文件测试

2. **数据准备**：
   - 使用.xlsx格式的Excel文件
   - 确保文件路径正确
   - 验证工作表名称存在

3. **界面操作**：
   - 左侧面板：配置参数
   - 右侧面板：监控和日志
   - 支持鼠标滚轮滚动

## ⚠️ 注意事项

1. **当前状态**：
   - Mac版本为预览版
   - 核心优化算法仍在开发中
   - 如需完整功能，建议使用Windows版本

2. **文件格式**：
   - 仅支持.xlsx格式的Excel文件
   - 不支持.xls格式（旧版Excel）

3. **权限问题**：
   - 确保对Excel文件有读取权限
   - 确保对输出目录有写入权限

## 🔧 故障排除

### 问题1: 提示缺少openpyxl库

```bash
# 解决方案
pip3 install openpyxl
```

### 问题2: Python版本过低

```bash
# 检查Python版本
python3 --version

# 如果版本低于3.7，请升级Python
```

### 问题3: 无法读取Excel文件

- 检查文件路径是否正确
- 确认工作表名称是否存在
- 验证文件格式为.xlsx
- 确保文件未被其他程序占用

### 问题4: 界面显示异常

- 确保使用Python 3.7+
- 检查tkinter是否正确安装
- 尝试调整窗口大小

### 问题5: macOS安全限制

```bash
# 如果提示安全限制，允许任何来源的应用
sudo spctl --master-disable

# 或者给予执行权限
chmod +x universal_media_optimizer_mac.py
```

## 🔄 版本对比

| 功能 | Windows版 | Mac版 |
|------|-----------|-------|
| 基本界面 | ✅ 完整 | ✅ 完整 |
| Excel读取 | ✅ win32com | ✅ openpyxl |
| 优化算法 | ✅ 完整 | 🚧 开发中 |
| 实时监控 | ✅ 完整 | ✅ 完整 |
| 滑块配比 | ✅ 完整 | 🚧 简化版 |
| Excel输出 | ✅ 美化版 | 🚧 开发中 |

## 🚀 未来计划

- [ ] 完整优化算法移植
- [ ] 滑块配比设置完善
- [ ] 美化Excel输出功能
- [ ] 更多预设配比模板
- [ ] 数据可视化图表

## 📞 技术支持

如遇问题，请提供以下信息：
- 操作系统版本 (macOS/Linux/Windows)
- Python版本
- 错误信息截图
- Excel文件格式

## 🔗 相关链接

- Python官网: https://www.python.org/
- openpyxl文档: https://openpyxl.readthedocs.io/
- Homebrew (macOS): https://brew.sh/

---

**版本**: v3.0 Mac预览版  
**更新日期**: 2025年6月22日  
**支持系统**: macOS, Linux, Windows  
**开发状态**: 预览版，持续开发中

感谢使用媒体预算优化工具Mac版！🍎
