# 媒体预算优化工具 v3.0 - 打包说明

## 📦 打包概述

我已经为您创建了完整的打包解决方案，可以将Python程序打包成Windows的.exe文件和Mac的.app应用程序。

## 🛠️ 打包脚本说明

### 1. `build_windows.py` - Windows打包脚本
- **功能**: 将Python程序打包成Windows可执行文件(.exe)
- **依赖**: PyInstaller
- **输出**: `媒体预算优化工具_v3.0_增强版.exe`
- **特点**: 
  - 自动安装PyInstaller
  - 包含所有必要的依赖库
  - 生成单文件可执行程序
  - 无需Python环境即可运行

### 2. `build_mac.py` - Mac打包脚本  
- **功能**: 将Python程序打包成Mac应用程序(.app)
- **依赖**: PyInstaller, openpyxl
- **输出**: `媒体预算优化工具_v3.0_Mac版.app`
- **特点**:
  - 跨平台兼容（macOS/Linux/Windows）
  - 可选择创建DMG安装包
  - 包含应用程序信息和文档类型关联

### 3. `build_all.py` - 全平台打包脚本
- **功能**: 自动检测系统并执行相应打包
- **特点**: 
  - 智能系统检测
  - 文件完整性检查
  - 创建完整发布包
  - 统一的打包流程

### 4. `install_mac_dependencies.py` - Mac依赖安装
- **功能**: 自动安装Mac版本所需的Python库
- **依赖**: openpyxl, pandas, numpy

## 🚀 使用方法

### Windows系统打包

```bash
# 方法1: 仅打包Windows版本
python build_windows.py

# 方法2: 使用全平台脚本（推荐）
python build_all.py
```

**输出结果**:
```
dist_windows_YYYYMMDD_HHMMSS/
├── 媒体预算优化工具_v3.0_增强版.exe    # 主程序
├── Gatorade simulation tool_cal.xlsx     # 示例文件
├── README_Mac版本.md                     # 说明文档
├── 更新完成总结.md                       # 更新说明
└── 使用说明.txt                          # Windows使用说明
```

### Mac系统打包

```bash
# 方法1: 仅打包Mac版本
python build_mac.py

# 方法2: 使用全平台脚本（推荐）  
python build_all.py
```

**输出结果**:
```
dist_mac_YYYYMMDD_HHMMSS/
├── 媒体预算优化工具_v3.0_Mac版.app       # Mac应用程序
├── Gatorade simulation tool_cal.xlsx     # 示例文件
├── README_Mac版本.md                     # 详细说明
├── 更新完成总结.md                       # 更新说明
├── install_mac_dependencies.py          # 依赖安装脚本
└── Mac使用说明.txt                       # Mac使用说明
```

### 全平台发布包

```bash
# 创建完整发布包（推荐）
python build_all.py
```

**输出结果**:
```
媒体预算优化工具_v3.0_完整版_YYYYMMDD_HHMMSS/
├── dist_windows_*/                       # Windows版本
├── dist_mac_*/                          # Mac版本  
├── 源码和文档/                          # 完整源码
└── README.txt                           # 总体说明
```

## ⚙️ 打包配置

### Windows配置 (build_windows.spec)
- **主程序**: `universal_media_optimizer_v2.py`
- **隐藏导入**: win32com, tkinter等Windows特定库
- **输出**: 单文件exe，无控制台窗口
- **图标**: 可自定义应用图标

### Mac配置 (build_mac.spec)  
- **主程序**: `universal_media_optimizer_mac.py`
- **隐藏导入**: openpyxl, tkinter等跨平台库
- **输出**: .app应用包
- **Bundle ID**: com.mediaoptimizer.v3
- **文档类型**: 支持Excel文件关联

## 📋 系统要求

### Windows打包环境
- Windows 10/11
- Python 3.7+
- PyInstaller
- win32com (pywin32)

### Mac打包环境
- macOS 10.12+
- Python 3.7+
- PyInstaller  
- openpyxl

### 运行环境要求

**Windows版本**:
- Windows 10/11 (64位)
- 4GB RAM
- 100MB 磁盘空间
- 无需Python环境

**Mac版本**:
- macOS 10.12 Sierra+
- 4GB RAM
- 100MB 磁盘空间
- 无需Python环境

## 🔧 故障排除

### 常见问题

#### 1. PyInstaller安装失败
```bash
# 解决方案
pip install --upgrade pip
pip install pyinstaller
```

#### 2. Windows打包缺少依赖
```bash
# 安装Windows特定依赖
pip install pywin32
```

#### 3. Mac应用无法打开
```bash
# 移除隔离属性
xattr -d com.apple.quarantine "应用名称.app"

# 或允许任何来源
sudo spctl --master-disable
```

#### 4. 打包文件过大
- 使用`--exclude-module`排除不需要的模块
- 使用UPX压缩可执行文件
- 考虑使用虚拟环境减少依赖

### 调试技巧

#### 启用调试模式
在spec文件中设置:
```python
debug=True,
console=True,  # 显示控制台输出
```

#### 查看依赖关系
```bash
# 分析导入的模块
pyi-archive_viewer dist/程序名.exe
```

## 📊 打包结果对比

| 特性 | Windows版 | Mac版 |
|------|-----------|-------|
| 文件大小 | ~50-80MB | ~60-90MB |
| 启动时间 | 5-15秒 | 10-30秒 |
| 功能完整性 | 100% | 80% (预览版) |
| 依赖要求 | 无 | 无 |
| 安装方式 | 直接运行 | 拖拽到应用程序 |

## 🎯 分发建议

### Windows分发
1. 将整个`dist_windows_*`目录打包成ZIP
2. 提供使用说明文档
3. 建议包含示例Excel文件
4. 可制作安装程序(NSIS/Inno Setup)

### Mac分发
1. 创建DMG安装包(推荐)
2. 或将.app文件打包成ZIP
3. 提供详细的安装说明
4. 考虑代码签名(需要开发者账号)

### 通用分发
1. 使用`build_all.py`创建完整发布包
2. 包含Windows和Mac两个版本
3. 提供源码供开发者参考
4. 详细的使用说明和故障排除指南

## 🔄 版本管理

建议的版本命名规则:
- `媒体预算优化工具_v3.0_增强版.exe` (Windows)
- `媒体预算优化工具_v3.0_Mac版.app` (Mac)
- `媒体预算优化工具_v3.0_完整版_YYYYMMDD` (发布包)

## 📞 技术支持

如果在打包过程中遇到问题:
1. 检查Python版本和依赖库
2. 查看打包日志中的错误信息
3. 确认所有源文件都存在
4. 尝试在虚拟环境中打包
5. 参考PyInstaller官方文档

---

**打包工具版本**: v3.0  
**支持平台**: Windows, macOS, Linux  
**更新日期**: 2025年6月22日
