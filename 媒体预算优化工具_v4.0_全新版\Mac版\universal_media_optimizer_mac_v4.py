#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
媒体预算优化工具 v4.0 Mac版 - 全新版
跨平台版本，使用openpyxl替代win32com
包含所有改进功能：界面美化、自适应布局、基准比例计算等
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
from datetime import datetime
import re
import sys
import os

class MediaOptimizerMacV4:
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.setup_styles()
        self.setup_variables()
        self.create_widgets()
        self.setup_layout()
        self.setup_bindings()

        # 数据存储
        self.channels_data = []
        self.channel_types = {}
        self.optimization_running = False
        self.current_best_kpi = 0
        self.baseline_kpi = 0

    def setup_window(self):
        """设置主窗口"""
        self.root.title("媒体预算优化工具 v4.0 Mac版 - 全新版")
        self.root.geometry("1600x1000")
        self.root.minsize(1400, 900)

        # 设置窗口样式
        self.root.configure(bg='#f0f0f0')

        # 窗口居中
        self.center_window()

        # 设置关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')

    def setup_styles(self):
        """设置美化样式"""
        self.style = ttk.Style()

        # 尝试使用更好的主题
        available_themes = self.style.theme_names()
        if 'aqua' in available_themes:  # macOS
            self.style.theme_use('aqua')
        elif 'clam' in available_themes:  # 跨平台
            self.style.theme_use('clam')
        else:
            self.style.theme_use('default')

        # 定义颜色主题
        self.colors = {
            'primary': '#007AFF',      # iOS蓝色
            'secondary': '#5856D6',    # iOS紫色
            'accent': '#FF9500',       # iOS橙色
            'success': '#34C759',      # iOS绿色
            'background': '#F2F2F7',   # iOS背景色
            'surface': '#FFFFFF',      # 表面色
            'text': '#1C1C1E',         # iOS文本色
            'text_light': '#8E8E93',   # iOS浅文本色
            'border': '#C6C6C8'        # iOS边框色
        }

        # 配置样式
        self.configure_styles()

    def configure_styles(self):
        """配置ttk样式"""
        # 主框架样式
        self.style.configure('Main.TFrame',
                           background=self.colors['background'],
                           relief='flat')

        # 卡片样式框架
        self.style.configure('Card.TFrame',
                           background=self.colors['surface'],
                           relief='solid',
                           borderwidth=1)

        # 标题标签样式
        self.style.configure('Title.TLabel',
                           background=self.colors['surface'],
                           foreground=self.colors['primary'],
                           font=('SF Pro Display', 14, 'bold'))

        # 子标题样式
        self.style.configure('Subtitle.TLabel',
                           background=self.colors['surface'],
                           foreground=self.colors['text'],
                           font=('SF Pro Display', 10, 'bold'))

        # 普通标签样式
        self.style.configure('Normal.TLabel',
                           background=self.colors['surface'],
                           foreground=self.colors['text'],
                           font=('SF Pro Display', 9))

        # 按钮样式
        self.style.configure('Primary.TButton',
                           font=('SF Pro Display', 10, 'bold'),
                           padding=(20, 10))

        self.style.configure('Secondary.TButton',
                           font=('SF Pro Display', 9),
                           padding=(15, 8))

        # 输入框样式
        self.style.configure('Modern.TEntry',
                           fieldbackground='white',
                           borderwidth=1,
                           relief='solid',
                           font=('SF Pro Display', 9))

        # 下拉框样式
        self.style.configure('Modern.TCombobox',
                           fieldbackground='white',
                           borderwidth=1,
                           relief='solid',
                           font=('SF Pro Display', 9))

    def setup_variables(self):
        """设置变量"""
        # 文件路径
        self.file_path_var = tk.StringVar()
        self.sheet_name_var = tk.StringVar()

        # 数据范围
        self.data_start_row_var = tk.StringVar()
        self.data_end_row_var = tk.StringVar()
        self.data_start_col_var = tk.StringVar()
        self.data_end_col_var = tk.StringVar()
        self.channel_row_var = tk.StringVar()

        # 基准比例设置
        self.baseline_method_var = tk.StringVar(value="manual")
        self.baseline_start_row_var = tk.StringVar()
        self.baseline_end_row_var = tk.StringVar()
        self.baseline_start_col_var = tk.StringVar()
        self.baseline_end_col_var = tk.StringVar()

        # 优化参数
        self.total_budget_var = tk.StringVar()
        self.num_solutions_var = tk.StringVar()
        self.iterations_var = tk.StringVar()
        self.kpi_type_var = tk.StringVar(value="Non-BHT")

        # 实时监控
        self.baseline_kpi_var = tk.StringVar(value="0")
        self.current_best_kpi_var = tk.StringVar(value="0")
        self.improvement_var = tk.StringVar(value="0%")
        self.progress_var = tk.StringVar(value="0%")
        self.status_var = tk.StringVar(value="就绪")

    def create_widgets(self):
        """创建所有界面组件"""
        # 主容器
        self.main_container = ttk.Frame(self.root, style='Main.TFrame')
        self.main_container.pack(fill='both', expand=True, padx=10, pady=10)

        # 创建左右分栏
        self.create_left_panel()
        self.create_right_panel()

    def create_left_panel(self):
        """创建左侧配置面板"""
        # 左侧滚动框架
        self.left_canvas = tk.Canvas(self.main_container, bg=self.colors['background'])
        self.left_scrollbar = ttk.Scrollbar(self.main_container, orient="vertical", command=self.left_canvas.yview)
        self.left_scrollable_frame = ttk.Frame(self.left_canvas, style='Main.TFrame')

        self.left_scrollable_frame.bind(
            "<Configure>",
            lambda e: self.left_canvas.configure(scrollregion=self.left_canvas.bbox("all"))
        )

        self.left_canvas.create_window((0, 0), window=self.left_scrollable_frame, anchor="nw")
        self.left_canvas.configure(yscrollcommand=self.left_scrollbar.set)

        # 创建配置卡片
        self.create_file_config_card()
        self.create_data_range_card()
        self.create_baseline_config_card()
        self.create_optimization_config_card()
        self.create_channel_constraints_card()

    def create_file_config_card(self):
        """创建文件配置卡片"""
        card = ttk.Frame(self.left_scrollable_frame, style='Card.TFrame')
        card.pack(fill='x', padx=5, pady=5)

        # 卡片标题
        title_frame = ttk.Frame(card, style='Card.TFrame')
        title_frame.pack(fill='x', padx=15, pady=(15, 10))

        ttk.Label(title_frame, text="📁 文件配置", style='Title.TLabel').pack(anchor='w')

        # 文件选择
        file_frame = ttk.Frame(card, style='Card.TFrame')
        file_frame.pack(fill='x', padx=15, pady=5)

        ttk.Label(file_frame, text="Excel文件:", style='Normal.TLabel').pack(anchor='w')

        file_input_frame = ttk.Frame(file_frame, style='Card.TFrame')
        file_input_frame.pack(fill='x', pady=(5, 0))

        self.file_entry = ttk.Entry(file_input_frame, textvariable=self.file_path_var,
                                   style='Modern.TEntry', state='readonly')
        self.file_entry.pack(side='left', fill='x', expand=True, padx=(0, 10))

        ttk.Button(file_input_frame, text="浏览", style='Secondary.TButton',
                  command=self.browse_file).pack(side='right')

        # 工作表选择
        sheet_frame = ttk.Frame(card, style='Card.TFrame')
        sheet_frame.pack(fill='x', padx=15, pady=5)

        ttk.Label(sheet_frame, text="工作表:", style='Normal.TLabel').pack(anchor='w')
        self.sheet_combo = ttk.Combobox(sheet_frame, textvariable=self.sheet_name_var,
                                       style='Modern.TCombobox', state='readonly')
        self.sheet_combo.pack(fill='x', pady=(5, 15))

    def on_closing(self):
        """窗口关闭时的处理"""
        if self.optimization_running:
            if messagebox.askokcancel("退出", "优化正在进行中，确定要退出吗？"):
                self.optimization_running = False
                self.root.destroy()
        else:
            self.root.destroy()

    def browse_file(self):
        """浏览文件"""
        filename = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[("Excel files", "*.xlsx *.xls")]
        )
        if filename:
            self.file_path_var.set(filename)
            self.load_sheet_names()

    def load_sheet_names(self):
        """加载工作表名称"""
        try:
            import openpyxl
            workbook = openpyxl.load_workbook(self.file_path_var.get(), read_only=True)
            sheet_names = workbook.sheetnames
            self.sheet_combo['values'] = sheet_names
            if sheet_names:
                self.sheet_name_var.set(sheet_names[0])
            workbook.close()
        except Exception as e:
            messagebox.showerror("错误", f"无法读取Excel文件: {str(e)}")

    def add_log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"

        if hasattr(self, 'log_text'):
            self.log_text.insert(tk.END, log_message)
            self.log_text.see(tk.END)
            self.root.update_idletasks()

    def run(self):
        """运行应用"""
        self.root.mainloop()

if __name__ == "__main__":
    app = MediaOptimizerMacV4()
    app.run()