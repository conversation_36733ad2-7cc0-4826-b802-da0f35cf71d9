# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['universal_media_optimizer_v2.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'win32com.client',
        'win32com.gen_py',
        'pythoncom',
        'pywintypes',
        'tkinter',
        'tkinter.ttk',
        'tkinter.filedialog',
        'tkinter.messagebox',
        'json',
        'threading',
        'datetime',
        'os',
        'time',
        'random',
        'universal_optimizer_engine_v2'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='媒体预算优化工具_v3.0_增强版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
