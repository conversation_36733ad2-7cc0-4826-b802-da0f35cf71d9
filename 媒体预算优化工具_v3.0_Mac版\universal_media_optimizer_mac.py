#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
媒体预算优化工具 v3.0 - Mac跨平台版
支持macOS、Linux和Windows系统
使用openpyxl替代win32com.client实现跨平台Excel操作
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import json
import os
import threading
import platform
from datetime import datetime

# 跨平台Excel处理
try:
    import openpyxl
    from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
    EXCEL_AVAILABLE = True
except ImportError:
    EXCEL_AVAILABLE = False
    print("警告: 未安装openpyxl库，Excel功能将不可用")
    print("请运行: pip install openpyxl")

class UniversalMediaOptimizerMac:
    def __init__(self, root):
        self.root = root
        self.root.title(f"媒体预算优化工具 v3.0 - Mac版 ({platform.system()})")

        # 根据系统调整窗口大小
        if platform.system() == "Darwin":  # macOS
            self.root.geometry("1400x900")
        else:
            self.root.geometry("1400x900")

        self.root.minsize(1200, 700)

        # 配置变量
        self.config = {
            "excel_file": "",
            "worksheet_name": "",
            "input_start_row": 1,
            "input_end_row": 1,
            "input_start_col": 1,
            "input_end_col": 1,
            "output_col": 1,
            "output_start_row": 1,
            "output_end_row": 1,
            "channel_names_row": 1,
            "total_budget": 1000000,
            "optimization_schemes": 5,
            "max_iterations": 200,
            "baseline_allocation": "equal",
            "custom_baseline_ratios": {},
            "channel_constraints": {},
            "kpi_type": "Non-BHT",
            "output_directory": os.path.expanduser("~/Desktop"),  # Mac默认桌面
            "output_prefix": "媒体预算优化结果"
        }

        self.channels = []
        self.workbook = None
        self.worksheet = None
        self.optimizer_engine = None

        self.create_widgets()
        self.load_config()

    def col_num_to_letter(self, col_num):
        """将列数字转换为字母（如1->A, 2->B, 27->AA）"""
        if col_num <= 0:
            return "A"
        result = ""
        while col_num > 0:
            col_num -= 1
            result = chr(col_num % 26 + ord('A')) + result
            col_num //= 26
        return result

    def col_letter_to_num(self, col_letter):
        """将列字母转换为数字（如A->1, B->2, AA->27）"""
        if not col_letter:
            return 1
        col_letter = col_letter.upper()
        result = 0
        for char in col_letter:
            if 'A' <= char <= 'Z':
                result = result * 26 + (ord(char) - ord('A') + 1)
            else:
                return 1
        return result

    def create_widgets(self):
        """创建界面组件 - Mac优化版"""
        # 检查Excel库可用性
        if not EXCEL_AVAILABLE:
            warning_frame = ttk.Frame(self.root)
            warning_frame.pack(fill=tk.X, padx=10, pady=5)

            warning_label = ttk.Label(warning_frame,
                text="⚠️ 缺少openpyxl库，请运行: pip install openpyxl",
                foreground="red", font=("Arial", 12, "bold"))
            warning_label.pack()

        # 创建主容器
        main_container = ttk.Frame(self.root, padding="10")
        main_container.pack(fill=tk.BOTH, expand=True)

        # 左侧面板 - 配置区域
        left_panel = ttk.Frame(main_container)
        left_panel.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))

        # 右侧面板 - 监控和结果区域
        right_panel = ttk.Frame(main_container)
        right_panel.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # 左侧滚动框架
        left_canvas = tk.Canvas(left_panel)
        left_scrollbar = ttk.Scrollbar(left_panel, orient="vertical", command=left_canvas.yview)
        left_scrollable_frame = ttk.Frame(left_canvas)

        left_scrollable_frame.bind(
            "<Configure>",
            lambda e: left_canvas.configure(scrollregion=left_canvas.bbox("all"))
        )

        left_canvas.create_window((0, 0), window=left_scrollable_frame, anchor="nw")
        left_canvas.configure(yscrollcommand=left_scrollbar.set)

        # 左侧配置区域
        config_frame = ttk.Frame(left_scrollable_frame, padding="5")
        config_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        self.create_config_sections(config_frame)

        # 左侧滚动配置
        left_canvas.pack(side="left", fill="both", expand=True)
        left_scrollbar.pack(side="right", fill="y")

        # 右侧面板内容
        self.create_monitor_panel(right_panel)

        # 绑定鼠标滚轮（跨平台）
        self.bind_mousewheel(left_canvas)

    def bind_mousewheel(self, canvas):
        """绑定鼠标滚轮事件（跨平台）"""
        def _on_mousewheel(event):
            if platform.system() == "Darwin":  # macOS
                canvas.yview_scroll(int(-1 * event.delta), "units")
            else:  # Windows/Linux
                canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")

        canvas.bind_all("<MouseWheel>", _on_mousewheel)  # Windows
        canvas.bind_all("<Button-4>", lambda e: canvas.yview_scroll(-1, "units"))  # Linux
        canvas.bind_all("<Button-5>", lambda e: canvas.yview_scroll(1, "units"))   # Linux

    def create_config_sections(self, parent):
        """创建配置区域"""
        # 1. 文件设置区域
        file_frame = ttk.LabelFrame(parent, text="1. 文件设置", padding="10")
        file_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)

        ttk.Label(file_frame, text="Excel文件:").grid(row=0, column=0, sticky=tk.W)
        self.file_var = tk.StringVar(value=self.config["excel_file"])
        ttk.Entry(file_frame, textvariable=self.file_var, width=50).grid(row=0, column=1, padx=5)
        ttk.Button(file_frame, text="浏览", command=self.browse_file).grid(row=0, column=2)

        ttk.Label(file_frame, text="工作表名称:").grid(row=1, column=0, sticky=tk.W)
        self.worksheet_var = tk.StringVar(value=self.config["worksheet_name"])
        ttk.Entry(file_frame, textvariable=self.worksheet_var, width=20).grid(row=1, column=1, sticky=tk.W, padx=5)

        # 2. 数据区域设置
        area_frame = ttk.LabelFrame(parent, text="2. 数据区域设置", padding="10")
        area_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)

        # 输入区域
        ttk.Label(area_frame, text="输入区域:").grid(row=0, column=0, sticky=tk.W)
        input_frame = ttk.Frame(area_frame)
        input_frame.grid(row=0, column=1, sticky=tk.W, padx=5)

        ttk.Label(input_frame, text="行:").grid(row=0, column=0)
        self.start_row_var = tk.IntVar(value=self.config["input_start_row"])
        ttk.Entry(input_frame, textvariable=self.start_row_var, width=5).grid(row=0, column=1)
        ttk.Label(input_frame, text="到").grid(row=0, column=2)
        self.end_row_var = tk.IntVar(value=self.config["input_end_row"])
        ttk.Entry(input_frame, textvariable=self.end_row_var, width=5).grid(row=0, column=3)

        ttk.Label(input_frame, text="列:").grid(row=0, column=4, padx=(10,0))
        self.start_col_var = tk.StringVar(value=self.col_num_to_letter(self.config["input_start_col"]))
        ttk.Entry(input_frame, textvariable=self.start_col_var, width=5).grid(row=0, column=5)
        ttk.Label(input_frame, text="到").grid(row=0, column=6)
        self.end_col_var = tk.StringVar(value=self.col_num_to_letter(self.config["input_end_col"]))
        ttk.Entry(input_frame, textvariable=self.end_col_var, width=5).grid(row=0, column=7)

        # 输出区域
        ttk.Label(area_frame, text="输出区域:").grid(row=1, column=0, sticky=tk.W)
        output_frame = ttk.Frame(area_frame)
        output_frame.grid(row=1, column=1, sticky=tk.W, padx=5)

        ttk.Label(output_frame, text="行:").grid(row=0, column=0)
        self.output_start_row_var = tk.IntVar(value=self.config["output_start_row"])
        ttk.Entry(output_frame, textvariable=self.output_start_row_var, width=5).grid(row=0, column=1)
        ttk.Label(output_frame, text="到").grid(row=0, column=2)
        self.output_end_row_var = tk.IntVar(value=self.config["output_end_row"])
        ttk.Entry(output_frame, textvariable=self.output_end_row_var, width=5).grid(row=0, column=3)

        ttk.Label(output_frame, text="列:").grid(row=0, column=4, padx=(10,0))
        self.output_col_var = tk.StringVar(value=self.col_num_to_letter(self.config["output_col"]))
        ttk.Entry(output_frame, textvariable=self.output_col_var, width=5).grid(row=0, column=5)

        # 同步按钮
        ttk.Button(output_frame, text="同步输入区域",
                  command=self.sync_output_with_input).grid(row=0, column=6, padx=(10,0))

        # 渠道名称行
        ttk.Label(area_frame, text="渠道名称行:").grid(row=2, column=0, sticky=tk.W)
        self.channel_row_var = tk.IntVar(value=self.config["channel_names_row"])
        ttk.Entry(area_frame, textvariable=self.channel_row_var, width=5).grid(row=2, column=1, sticky=tk.W, padx=5)

        # KPI类型选择
        ttk.Label(area_frame, text="KPI类型:").grid(row=3, column=0, sticky=tk.W)
        self.kpi_type_var = tk.StringVar(value=self.config["kpi_type"])
        kpi_type_frame = ttk.Frame(area_frame)
        kpi_type_frame.grid(row=3, column=1, sticky=tk.W, padx=5)

        ttk.Radiobutton(kpi_type_frame, text="Non-BHT (求和)", variable=self.kpi_type_var,
                       value="Non-BHT").grid(row=0, column=0, sticky=tk.W)
        ttk.Radiobutton(kpi_type_frame, text="BHT (平均)", variable=self.kpi_type_var,
                       value="BHT").grid(row=0, column=1, sticky=tk.W, padx=(20,0))

        # 添加KPI类型说明
        kpi_info_label = ttk.Label(area_frame, text="💡 Non-BHT: 各行KPI求和 | BHT: 各行KPI平均值",
                                  foreground="blue", font=("Arial", 8))
        kpi_info_label.grid(row=4, column=0, columnspan=2, sticky=tk.W, pady=(5,0))

        # 继续添加其他配置区域...
        self.create_remaining_sections(parent)

    def create_remaining_sections(self, parent):
        """创建剩余的配置区域"""
        # 3. 预算和优化设置
        budget_frame = ttk.LabelFrame(parent, text="3. 预算和优化设置", padding="15")
        budget_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)

        # 第一行：预算设置
        ttk.Label(budget_frame, text="总预算:", font=("Arial", 9, "bold")).grid(row=0, column=0, sticky=tk.W, pady=2)
        self.budget_var = tk.IntVar(value=self.config["total_budget"])
        budget_entry = ttk.Entry(budget_frame, textvariable=self.budget_var, width=15, font=("Arial", 9))
        budget_entry.grid(row=0, column=1, sticky=tk.W, padx=(5,20), pady=2)
        ttk.Label(budget_frame, text="元", foreground="gray").grid(row=0, column=2, sticky=tk.W, pady=2)

        # 第二行：优化参数
        ttk.Label(budget_frame, text="优化方案数量:", font=("Arial", 9, "bold")).grid(row=1, column=0, sticky=tk.W, pady=2)
        self.schemes_var = tk.IntVar(value=self.config["optimization_schemes"])
        schemes_entry = ttk.Entry(budget_frame, textvariable=self.schemes_var, width=8, font=("Arial", 9))
        schemes_entry.grid(row=1, column=1, sticky=tk.W, padx=(5,20), pady=2)
        ttk.Label(budget_frame, text="个", foreground="gray").grid(row=1, column=2, sticky=tk.W, pady=2)

        ttk.Label(budget_frame, text="迭代次数:", font=("Arial", 9, "bold")).grid(row=1, column=3, sticky=tk.W, padx=(30,0), pady=2)
        self.iterations_var = tk.IntVar(value=self.config.get("max_iterations", 200))
        iterations_entry = ttk.Entry(budget_frame, textvariable=self.iterations_var, width=8, font=("Arial", 9))
        iterations_entry.grid(row=1, column=4, sticky=tk.W, padx=(5,20), pady=2)
        ttk.Label(budget_frame, text="次", foreground="gray").grid(row=1, column=5, sticky=tk.W, pady=2)

        # 第三行：基准方案设置
        ttk.Label(budget_frame, text="基准方案:", font=("Arial", 9, "bold")).grid(row=2, column=0, sticky=tk.W, pady=2)
        self.baseline_var = tk.StringVar(value=self.config["baseline_allocation"])
        baseline_combo = ttk.Combobox(budget_frame, textvariable=self.baseline_var,
                                    values=["equal", "custom"], width=12, font=("Arial", 9))
        baseline_combo.grid(row=2, column=1, sticky=tk.W, padx=(5,20), pady=2)
        baseline_combo.bind("<<ComboboxSelected>>", self.on_baseline_changed)

        self.custom_baseline_btn = ttk.Button(budget_frame, text="设置自定义配比",
                                            command=self.set_custom_baseline, state="disabled")
        self.custom_baseline_btn.grid(row=2, column=3, columnspan=2, sticky=tk.W, padx=(30,0), pady=2)

        # 4. 输出设置
        output_frame = ttk.LabelFrame(parent, text="4. 输出设置", padding="15")
        output_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)

        # 输出目录设置
        ttk.Label(output_frame, text="输出目录:", font=("Arial", 9, "bold")).grid(row=0, column=0, sticky=tk.W, pady=2)
        self.output_dir_var = tk.StringVar(value=self.config.get("output_directory", os.path.expanduser("~/Desktop")))
        output_dir_entry = ttk.Entry(output_frame, textvariable=self.output_dir_var, width=40, font=("Arial", 9))
        output_dir_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(5,10), pady=2)
        ttk.Button(output_frame, text="📁 浏览", command=self.browse_output_directory).grid(row=0, column=2, padx=5, pady=2)

        # 文件名前缀设置
        ttk.Label(output_frame, text="文件名前缀:", font=("Arial", 9, "bold")).grid(row=1, column=0, sticky=tk.W, pady=2)
        self.output_prefix_var = tk.StringVar(value=self.config.get("output_prefix", "媒体预算优化结果"))
        output_prefix_entry = ttk.Entry(output_frame, textvariable=self.output_prefix_var, width=25, font=("Arial", 9))
        output_prefix_entry.grid(row=1, column=1, sticky=tk.W, padx=(5,10), pady=2)
        ttk.Label(output_frame, text="💡 支持中文名称", foreground="blue", font=("Arial", 8)).grid(row=1, column=2, sticky=tk.W, padx=5, pady=2)

        # 配置列权重，使输入框可以拉伸
        output_frame.columnconfigure(1, weight=1)

        # 5. 操作控制
        self.create_control_section(parent)

    def create_control_section(self, parent):
        """创建操作控制区域"""
        # 5. 渠道约束设置
        constraint_frame = ttk.LabelFrame(parent, text="5. 渠道约束设置", padding="15")
        constraint_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)

        button_constraint_frame = ttk.Frame(constraint_frame)
        button_constraint_frame.grid(row=0, column=0, columnspan=3, sticky=(tk.W, tk.E))

        ttk.Button(button_constraint_frame, text="读取渠道信息",
                  command=self.load_channels).grid(row=0, column=0, pady=5)
        ttk.Button(button_constraint_frame, text="设置渠道约束",
                  command=self.set_channel_constraints).grid(row=0, column=1, padx=5, pady=5)

        # 默认约束说明
        ttk.Label(button_constraint_frame, text="默认约束范围: 2%-60% (可修改)",
                 foreground="blue").grid(row=0, column=2, padx=10)

        # 渠道信息显示
        self.channel_text = tk.Text(constraint_frame, height=8, width=60)
        self.channel_text.grid(row=1, column=0, columnspan=2, pady=5)

        channel_scrollbar = ttk.Scrollbar(constraint_frame, orient="vertical", command=self.channel_text.yview)
        channel_scrollbar.grid(row=1, column=2, sticky=(tk.N, tk.S))
        self.channel_text.configure(yscrollcommand=channel_scrollbar.set)

        # 6. 操作按钮
        button_frame = ttk.LabelFrame(parent, text="6. 操作控制", padding="15")
        button_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=10)

        # 第一行：配置操作
        config_frame = ttk.LabelFrame(button_frame, text="配置管理", padding="10")
        config_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)

        ttk.Button(config_frame, text="📊 读取渠道", command=self.load_channels, width=12).grid(row=0, column=0, padx=5, pady=2)
        ttk.Button(config_frame, text="⚙️ 设置约束", command=self.set_channel_constraints, width=12).grid(row=0, column=1, padx=5, pady=2)
        ttk.Button(config_frame, text="💾 保存配置", command=self.save_config, width=12).grid(row=0, column=2, padx=5, pady=2)
        ttk.Button(config_frame, text="📂 加载配置", command=self.load_config_file, width=12).grid(row=0, column=3, padx=5, pady=2)

        # 第二行：优化控制
        control_frame = ttk.LabelFrame(button_frame, text="优化控制", padding="10")
        control_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)

        self.start_btn = ttk.Button(control_frame, text="🚀 开始优化", command=self.start_optimization, width=15)
        self.start_btn.grid(row=0, column=0, padx=10, pady=5)

        self.pause_btn = ttk.Button(control_frame, text="⏸️ 暂停", command=self.pause_optimization, state="disabled", width=12)
        self.pause_btn.grid(row=0, column=1, padx=5, pady=5)

        self.resume_btn = ttk.Button(control_frame, text="▶️ 恢复", command=self.resume_optimization, state="disabled", width=12)
        self.resume_btn.grid(row=0, column=2, padx=5, pady=5)

        self.stop_btn = ttk.Button(control_frame, text="⏹️ 终止", command=self.terminate_optimization, state="disabled", width=12)
        self.stop_btn.grid(row=0, column=3, padx=5, pady=5)

        # 7. 优化进度
        progress_frame = ttk.LabelFrame(parent, text="7. 优化进度", padding="15")
        progress_frame.grid(row=6, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)

        # 进度条
        progress_label = ttk.Label(progress_frame, text="优化进度:", font=("Arial", 9, "bold"))
        progress_label.grid(row=0, column=0, sticky=tk.W, pady=2)

        self.progress = ttk.Progressbar(progress_frame, mode='determinate', length=400)
        self.progress.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10,0), pady=2)

        # 状态信息
        status_label = ttk.Label(progress_frame, text="当前状态:", font=("Arial", 9, "bold"))
        status_label.grid(row=1, column=0, sticky=tk.W, pady=2)

        self.status_var = tk.StringVar(value="🟢 就绪")
        self.status_label = ttk.Label(progress_frame, textvariable=self.status_var, font=("Arial", 9))
        self.status_label.grid(row=1, column=1, sticky=tk.W, padx=(10,0), pady=2)

        # 详细进度信息
        detail_label = ttk.Label(progress_frame, text="详细信息:", font=("Arial", 9, "bold"))
        detail_label.grid(row=2, column=0, sticky=tk.W, pady=2)

        self.progress_detail_var = tk.StringVar(value="等待开始优化...")
        self.progress_detail_label = ttk.Label(progress_frame, textvariable=self.progress_detail_var,
                                              font=("Arial", 9), foreground="blue")
        self.progress_detail_label.grid(row=2, column=1, sticky=tk.W, padx=(10,0), pady=2)

        # 配置列权重
        progress_frame.columnconfigure(1, weight=1)

    def create_monitor_panel(self, right_panel):
        """创建右侧监控面板"""
        # 实时监控窗口
        monitor_frame = ttk.LabelFrame(right_panel, text="📊 实时监控仪表板", padding="15")
        monitor_frame.pack(fill=tk.X, pady=(0, 10))

        # 监控网格布局
        monitor_grid = ttk.Frame(monitor_frame)
        monitor_grid.pack(fill=tk.X)

        # KPI指标行
        kpi_row = ttk.Frame(monitor_grid)
        kpi_row.pack(fill=tk.X, pady=5)

        # 基准KPI卡片
        baseline_card = ttk.LabelFrame(kpi_row, text="📈 基准KPI", padding="10")
        baseline_card.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))

        self.baseline_kpi_var = tk.StringVar(value="未计算")
        baseline_value = ttk.Label(baseline_card, textvariable=self.baseline_kpi_var,
                                  font=("Arial", 12, "bold"), foreground="#0066CC")
        baseline_value.pack()

        # 当前最优KPI卡片
        best_card = ttk.LabelFrame(kpi_row, text="🏆 当前最优KPI", padding="10")
        best_card.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

        self.best_kpi_var = tk.StringVar(value="未计算")
        best_value = ttk.Label(best_card, textvariable=self.best_kpi_var,
                              font=("Arial", 12, "bold"), foreground="#008000")
        best_value.pack()

        # 提升幅度卡片
        improvement_card = ttk.LabelFrame(kpi_row, text="📊 提升幅度", padding="10")
        improvement_card.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 0))

        self.improvement_var = tk.StringVar(value="0.00%")
        improvement_value = ttk.Label(improvement_card, textvariable=self.improvement_var,
                                     font=("Arial", 12, "bold"), foreground="#FF6600")
        improvement_value.pack()

        # 状态指标行
        status_row = ttk.Frame(monitor_grid)
        status_row.pack(fill=tk.X, pady=5)

        # 优化状态卡片
        status_card = ttk.LabelFrame(status_row, text="⚡ 优化状态", padding="10")
        status_card.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))

        self.optimization_status_var = tk.StringVar(value="🟢 就绪")
        status_label = ttk.Label(status_card, textvariable=self.optimization_status_var,
                                font=("Arial", 10, "bold"))
        status_label.pack()

        # 系统信息卡片
        system_card = ttk.LabelFrame(status_row, text="💻 系统信息", padding="10")
        system_card.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 0))

        system_info = f"{platform.system()} {platform.release()}"
        system_label = ttk.Label(system_card, text=system_info,
                                font=("Arial", 9), foreground="#666666")
        system_label.pack()

        # 实时日志
        log_frame = ttk.LabelFrame(monitor_grid, text="📝 实时日志", padding="10")
        log_frame.pack(fill=tk.X, pady=(10, 0))

        log_container = ttk.Frame(log_frame)
        log_container.pack(fill=tk.X)

        self.log_text = tk.Text(log_container, height=6, width=50, wrap=tk.WORD,
                               font=("Monaco", 9) if platform.system() == "Darwin" else ("Consolas", 9),
                               bg="#F8F9FA")
        log_scrollbar = ttk.Scrollbar(log_container, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)

        self.log_text.pack(side="left", fill="both", expand=True)
        log_scrollbar.pack(side="right", fill="y")

        # 初始化日志
        self.add_log("📊 Mac版监控系统已启动")
        self.add_log(f"💻 运行环境: {platform.system()} {platform.release()}")
        self.add_log("💡 配置完成后点击'开始优化'")
        self.log_text.config(state=tk.DISABLED)

        # 结果预览区域
        preview_frame = ttk.LabelFrame(right_panel, text="📋 结果预览", padding="15")
        preview_frame.pack(fill=tk.BOTH, expand=True)

        # 结果预览文本框
        self.preview_text = tk.Text(preview_frame, height=15, width=50, wrap=tk.WORD,
                                   font=("Monaco", 9) if platform.system() == "Darwin" else ("Consolas", 9))
        preview_scrollbar = ttk.Scrollbar(preview_frame, orient="vertical", command=self.preview_text.yview)
        self.preview_text.configure(yscrollcommand=preview_scrollbar.set)

        self.preview_text.pack(side="left", fill="both", expand=True)
        preview_scrollbar.pack(side="right", fill="y")

        # 初始化预览内容
        self.preview_text.insert(tk.END, "📊 优化结果预览\n\n")
        self.preview_text.insert(tk.END, "等待开始优化...\n\n")
        self.preview_text.insert(tk.END, "💡 提示：\n")
        self.preview_text.insert(tk.END, "• 配置完成后点击'开始优化'\n")
        self.preview_text.insert(tk.END, "• 优化过程中可实时查看进度\n")
        self.preview_text.insert(tk.END, "• 完成后可在此预览结果摘要\n")
        self.preview_text.insert(tk.END, f"• 当前系统: {platform.system()}\n")
        self.preview_text.config(state=tk.DISABLED)

    def add_log(self, message):
        """添加日志信息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)

    # 基本方法实现
    def sync_output_with_input(self):
        """同步输出区域与输入区域"""
        self.output_start_row_var.set(self.start_row_var.get())
        self.output_end_row_var.set(self.end_row_var.get())

    def on_baseline_changed(self, event=None):
        """基准方案选择改变时的回调"""
        if self.baseline_var.get() == "custom":
            self.custom_baseline_btn.config(state="normal")
        else:
            self.custom_baseline_btn.config(state="disabled")

    def browse_file(self):
        """浏览选择Excel文件"""
        filename = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[("Excel files", "*.xlsx *.xls"), ("All files", "*.*")]
        )
        if filename:
            self.file_var.set(filename)
            self.add_log(f"📁 已选择文件: {os.path.basename(filename)}")

    def browse_output_directory(self):
        """浏览输出目录"""
        directory = filedialog.askdirectory(
            title="选择输出目录",
            initialdir=self.output_dir_var.get()
        )
        if directory:
            self.output_dir_var.set(directory)
            self.add_log(f"📂 输出目录: {directory}")

    def load_channels(self):
        """读取渠道信息 - Mac版使用openpyxl"""
        if not EXCEL_AVAILABLE:
            messagebox.showerror("错误", "缺少openpyxl库，请运行: pip install openpyxl")
            return

        try:
            excel_file = self.file_var.get()
            if not excel_file or not os.path.exists(excel_file):
                messagebox.showerror("错误", "请先选择有效的Excel文件")
                return

            self.add_log("📊 正在读取渠道信息...")

            # 使用openpyxl读取Excel
            self.workbook = openpyxl.load_workbook(excel_file, data_only=True)
            worksheet_name = self.worksheet_var.get()

            if worksheet_name not in self.workbook.sheetnames:
                messagebox.showerror("错误", f"工作表 '{worksheet_name}' 不存在")
                return

            self.worksheet = self.workbook[worksheet_name]

            # 读取渠道名称
            self.channels = []
            start_col = self.col_letter_to_num(self.start_col_var.get())
            end_col = self.col_letter_to_num(self.end_col_var.get())
            channel_row = self.channel_row_var.get()

            for col in range(start_col, end_col + 1):
                cell_value = self.worksheet.cell(row=channel_row, column=col).value
                if cell_value:
                    self.channels.append(str(cell_value).strip())

            # 显示渠道信息
            self.channel_text.delete(1.0, tk.END)
            self.channel_text.insert(tk.END, f"成功读取到 {len(self.channels)} 个媒体渠道:\n\n")
            for i, channel in enumerate(self.channels, 1):
                self.channel_text.insert(tk.END, f"{i:2d}. {channel}\n")

            self.add_log(f"✅ 成功读取 {len(self.channels)} 个渠道")

        except Exception as e:
            messagebox.showerror("错误", f"读取渠道信息失败: {str(e)}")
            self.add_log(f"❌ 读取失败: {str(e)}")

    # 占位方法（简化版）
    def set_custom_baseline(self):
        """设置自定义基准配比 - 简化版"""
        messagebox.showinfo("提示", "Mac版暂时使用简化的配比设置")

    def set_channel_constraints(self):
        """设置渠道约束 - 简化版"""
        messagebox.showinfo("提示", "Mac版暂时使用默认约束设置")

    def save_config(self):
        """保存配置"""
        messagebox.showinfo("提示", "Mac版配置保存功能开发中")

    def load_config_file(self):
        """加载配置文件"""
        messagebox.showinfo("提示", "Mac版配置加载功能开发中")

    def load_config(self):
        """加载默认配置"""
        pass  # 简化版暂不实现

    def start_optimization(self):
        """开始优化 - 简化版"""
        if not EXCEL_AVAILABLE:
            messagebox.showerror("错误", "缺少openpyxl库，无法进行优化")
            return

        if not self.channels:
            messagebox.showerror("错误", "请先读取渠道信息")
            return

        self.add_log("🚀 开始优化...")
        messagebox.showinfo("提示", "Mac版优化功能开发中，请使用Windows版本进行完整优化")

    def pause_optimization(self):
        """暂停优化"""
        self.add_log("⏸️ 暂停优化")

    def resume_optimization(self):
        """恢复优化"""
        self.add_log("▶️ 恢复优化")

    def terminate_optimization(self):
        """终止优化"""
        self.add_log("⏹️ 终止优化")


def main():
    """主函数"""
    root = tk.Tk()

    # 设置应用图标（如果有的话）
    try:
        if platform.system() == "Darwin":  # macOS
            root.tk.call('wm', 'iconbitmap', root._w, '-default', '')
    except:
        pass

    app = UniversalMediaOptimizerMac(root)

    # 显示启动信息
    if not EXCEL_AVAILABLE:
        messagebox.showwarning(
            "依赖缺失",
            "检测到缺少openpyxl库。\n\n"
            "请在终端中运行以下命令安装：\n"
            "pip install openpyxl\n\n"
            "安装完成后重启应用。"
        )

    root.mainloop()


if __name__ == "__main__":
    main()