#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试v4.0完整功能版本的所有功能
"""

import os
import sys
import traceback

def test_imports():
    """测试所有必要的导入"""
    print("🔍 测试导入...")
    
    try:
        import tkinter as tk
        print("✅ tkinter导入成功")
        
        from tkinter import ttk, filedialog, messagebox, scrolledtext
        print("✅ tkinter子模块导入成功")
        
        import win32com.client
        print("✅ win32com.client导入成功")
        
        import threading
        print("✅ threading导入成功")
        
        from datetime import datetime
        print("✅ datetime导入成功")
        
        import random
        print("✅ random导入成功")
        
        import time
        print("✅ time导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_excel_file():
    """测试Excel文件是否存在"""
    print("\n📊 测试Excel文件...")
    
    excel_file = "Gatorade simulation tool_cal.xlsx"
    
    if os.path.exists(excel_file):
        print(f"✅ Excel文件存在: {excel_file}")
        
        # 测试Excel连接
        try:
            import win32com.client
            excel_app = win32com.client.Dispatch("Excel.Application")
            excel_app.Visible = False
            excel_app.DisplayAlerts = False
            
            workbook = excel_app.Workbooks.Open(os.path.abspath(excel_file))
            sheet_names = [sheet.Name for sheet in workbook.Worksheets]
            
            print(f"✅ Excel文件可以打开，包含工作表: {sheet_names}")
            
            # 测试读取数据
            worksheet = workbook.Worksheets(sheet_names[0])
            
            # 测试读取第1行的数据（渠道名称）
            test_data = []
            for col in range(1, 11):  # 测试前10列
                cell_value = worksheet.Cells(1, col).Value
                if cell_value:
                    test_data.append(str(cell_value))
                    
            print(f"✅ 读取到测试数据: {test_data[:5]}...")  # 只显示前5个
            
            workbook.Close(SaveChanges=False)
            excel_app.Quit()
            
            return True
            
        except Exception as e:
            print(f"❌ Excel连接测试失败: {e}")
            return False
            
    else:
        print(f"❌ Excel文件不存在: {excel_file}")
        return False

def test_program_creation():
    """测试程序创建"""
    print("\n🚀 测试程序创建...")
    
    try:
        from media_optimizer_v4_complete import MediaOptimizerV4Complete
        print("✅ 程序类导入成功")
        
        # 创建程序实例（不启动主循环）
        app = MediaOptimizerV4Complete()
        print("✅ 程序实例创建成功")
        
        # 测试基本属性
        if hasattr(app, 'channels_data'):
            print("✅ 渠道数据属性存在")
        
        if hasattr(app, 'channel_types'):
            print("✅ 渠道类型属性存在")
            
        if hasattr(app, 'channel_constraints'):
            print("✅ 渠道约束属性存在")
            
        if hasattr(app, 'file_path_var'):
            print("✅ 文件路径变量存在")
            
        if hasattr(app, 'baseline_method_var'):
            print("✅ 基准方法变量存在")
            
        # 测试方法存在
        if hasattr(app, 'read_channels'):
            print("✅ 读取渠道方法存在")
            
        if hasattr(app, 'display_channel_classification'):
            print("✅ 渠道分类方法存在")
            
        if hasattr(app, 'start_optimization'):
            print("✅ 开始优化方法存在")
            
        if hasattr(app, 'calculate_baseline_kpi'):
            print("✅ 基准KPI计算方法存在")
            
        # 关闭程序
        app.root.destroy()
        print("✅ 程序正常关闭")
        
        return True
        
    except Exception as e:
        print(f"❌ 程序创建测试失败: {e}")
        traceback.print_exc()
        return False

def test_layout_responsiveness():
    """测试布局响应性"""
    print("\n📐 测试布局响应性...")
    
    try:
        from media_optimizer_v4_complete import MediaOptimizerV4Complete
        
        app = MediaOptimizerV4Complete()
        
        # 测试窗口大小变化
        original_size = "1400x900"
        app.root.geometry(original_size)
        app.root.update()
        print(f"✅ 设置窗口大小: {original_size}")
        
        # 测试最小化窗口
        min_size = "1200x700"
        app.root.geometry(min_size)
        app.root.update()
        print(f"✅ 设置最小窗口大小: {min_size}")
        
        # 测试最大化窗口
        max_size = "1920x1080"
        app.root.geometry(max_size)
        app.root.update()
        print(f"✅ 设置最大窗口大小: {max_size}")
        
        # 测试布局组件是否存在
        if hasattr(app, 'main_frame'):
            print("✅ 主框架存在")
            
        if hasattr(app, 'left_frame'):
            print("✅ 左侧框架存在")
            
        if hasattr(app, 'right_frame'):
            print("✅ 右侧框架存在")
            
        if hasattr(app, 'left_canvas'):
            print("✅ 左侧滚动画布存在")
            
        app.root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ 布局测试失败: {e}")
        return False

def test_channel_classification():
    """测试渠道分类功能"""
    print("\n🎯 测试渠道分类功能...")
    
    try:
        from media_optimizer_v4_complete import MediaOptimizerV4Complete
        
        app = MediaOptimizerV4Complete()
        
        # 模拟渠道数据
        test_channels = ["百度", "谷歌", "微信", "微博", "抖音"]
        app.channels_data = test_channels
        print(f"✅ 设置测试渠道: {test_channels}")
        
        # 测试渠道分类界面创建
        app.display_channel_classification()
        print("✅ 渠道分类界面创建成功")
        
        # 测试渠道控件是否创建
        if hasattr(app, 'channel_widgets'):
            print(f"✅ 渠道控件创建成功，数量: {len(app.channel_widgets)}")
            
            # 测试每个渠道的控件
            for channel in test_channels:
                if channel in app.channel_widgets:
                    widgets = app.channel_widgets[channel]
                    if 'type_var' in widgets and 'min_var' in widgets and 'max_var' in widgets:
                        print(f"✅ 渠道 {channel} 的控件完整")
                    else:
                        print(f"⚠️ 渠道 {channel} 的控件不完整")
                        
        app.root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ 渠道分类测试失败: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("🧪 开始运行v4.0完整功能测试...")
    print("=" * 50)
    
    tests = [
        ("导入测试", test_imports),
        ("Excel文件测试", test_excel_file),
        ("程序创建测试", test_program_creation),
        ("布局响应性测试", test_layout_responsiveness),
        ("渠道分类测试", test_channel_classification)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                print(f"✅ {test_name} 通过")
                passed += 1
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！v4.0完整功能版本可以正常使用！")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
        return False

if __name__ == "__main__":
    try:
        success = run_all_tests()
        
        if success:
            print("\n🚀 建议使用以下命令启动完整版本:")
            print("   python media_optimizer_v4_complete.py")
            print("   或双击: start_complete.bat")
        else:
            print("\n🔧 请修复失败的测试后再使用")
            
    except Exception as e:
        print(f"测试运行失败: {e}")
        traceback.print_exc()
    
    input("\n按回车键退出...")
