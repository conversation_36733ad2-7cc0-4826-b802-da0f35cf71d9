# 媒体预算优化工具 v3.0 增强版 - 分发版

## 🎉 欢迎使用v3.0增强版！

这是包含所有最新功能的媒体预算优化工具v3.0版本。

## 🚀 快速启动

### 推荐方式（一键启动）
双击运行 "启动v3.0.bat" 文件
- 自动检查Python环境
- 自动安装缺失依赖
- 启动完整v3.0功能

### 手动方式
1. 确保已安装Python 3.7+
2. 安装依赖：pip install pywin32
3. 运行：python 启动器.py

## 🆕 v3.0 全新功能

### 1. KPI类型选择 ✨
- Non-BHT: 各行KPI求和（传统方式）
- BHT: 各行KPI平均值（新增）
- 在界面中可轻松切换

### 2. 16:9屏幕优化布局 ✨
- 左右分栏设计，适配宽屏显示器
- 左侧：配置区域
- 右侧：实时监控和结果预览
- 窗口尺寸：1400x900，最小1200x700

### 3. 实时监控仪表板 ✨
- 📈 基准KPI实时显示
- 🏆 当前最优KPI跟踪
- 📊 提升幅度计算
- ⚡ 优化状态监控
- 🔄 迭代进度显示
- 📋 生成方案计数
- 📝 实时日志记录

### 4. 滑块配比设置 ✨
- 直观的滑块界面设置自定义配比
- 实时显示总配比和数值
- 智能预设功能：
  - 🟰 平均分配
  - 🧠 智能分配（数字媒体40%，社交30%，其他30%）
  - 🔄 重置清零
- 支持精确数值输入

### 5. 美化Excel输出 ✨
- 专业级的报告格式
- 彩色表头和条件格式
- 图标和样式美化
- 移除txt文件输出，只保留Excel
- 标题合并、边框、字体颜色等

### 6. 列字母输入 ✨
- 支持Excel风格的列字母输入（A、B、C等）
- 自动转换数字和字母格式
- 更符合Excel使用习惯

### 7. 结果预览功能 ✨
- 优化完成后先显示结果摘要
- 显示最优方案排行榜
- 显示预算分配详情
- 用户确认满意后再选择保存位置

### 8. 移除默认值 ✨
- 所有输入字段无默认值
- 用户需主动配置所有参数
- 避免使用错误的默认配置

## 📁 文件说明

- 启动v3.0.bat - 一键启动脚本（推荐）
- 启动器.py - Python启动器
- universal_media_optimizer_v2.py - v3.0主程序
- universal_optimizer_engine_v2.py - v3.0优化引擎
- Gatorade simulation tool_cal.xlsx - 示例Excel文件
- 使用说明.txt - 本文件

## 💡 使用技巧

### 首次使用
1. 建议先用示例Excel文件熟悉功能
2. 所有配置项都需要手动填写（无默认值）
3. 选择合适的KPI类型（BHT vs Non-BHT）

### 数据准备
1. 使用.xlsx格式的Excel文件
2. 确保数据区域连续且格式正确
3. 渠道名称行要包含所有渠道名称

### 界面操作
1. 左侧面板：配置所有参数
2. 右侧面板：实时查看进度和结果
3. 支持鼠标滚轮滚动
4. 可调整窗口大小

### 优化设置
1. 方案数量建议3-6个
2. 迭代次数建议200-500次
3. 根据项目选择合适的KPI类型
4. 使用滑块设置自定义配比

## ⚠️ 系统要求

- Windows 10/11 (64位)
- Python 3.7 或更高版本
- 4GB RAM 或更多
- 1920x1080 或更高分辨率（推荐）
- Excel文件读写权限

## 🔧 故障排除

### 问题1: 提示未检测到Python
解决方案：
1. 下载安装Python 3.7+：https://www.python.org/downloads/
2. 安装时勾选"Add Python to PATH"
3. 重新打开命令行窗口

### 问题2: 缺少pywin32库
解决方案：
```
pip install pywin32
```

### 问题3: 无法读取Excel文件
解决方案：
1. 检查文件路径是否正确
2. 确认工作表名称存在
3. 验证文件格式为.xlsx
4. 确保文件未被其他程序占用

### 问题4: 界面显示异常
解决方案：
1. 调整屏幕分辨率到1920x1080或更高
2. 检查系统缩放设置
3. 尝试重启程序

### 问题5: 优化过程中出错
解决方案：
1. 检查数据区域设置是否正确
2. 确认渠道名称行配置无误
3. 验证预算和约束设置合理
4. 查看实时日志中的错误信息

## 📞 技术支持

如果遇到问题：
1. 查看实时日志中的详细错误信息
2. 参考示例Excel文件格式
3. 检查配置参数是否合理
4. 提供错误截图和系统信息

## 🔄 版本信息

- 版本: v3.0 增强版
- 发布日期: 2025年6月22日
- 支持系统: Windows 10/11
- 核心功能: 完整版（包含所有新功能）
- 运行方式: Python源码（确保功能完整性）

---

🎉 感谢使用媒体预算优化工具v3.0！
祝您优化工作顺利！🚀
