#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
cx_Freeze setup script for Media Budget Optimizer v3.0
"""

import sys
from cx_Freeze import setup, Executable

# 依赖包
packages = [
    "tkinter",
    "tkinter.ttk", 
    "tkinter.filedialog",
    "tkinter.messagebox",
    "win32com.client",
    "json",
    "os",
    "threading",
    "datetime",
    "time",
    "random"
]

# 包含的文件
include_files = [
    "universal_optimizer_engine_v2.py"
]

# 构建选项
build_exe_options = {
    "packages": packages,
    "include_files": include_files,
    "excludes": ["numpy", "matplotlib", "scipy"],  # 排除不需要的大型库
    "optimize": 2
}

# 可执行文件配置
exe = Executable(
    script="universal_media_optimizer_v2.py",
    base="Win32GUI",  # Windows GUI应用
    target_name="媒体预算优化工具_v3.0_增强版.exe",
    icon=None
)

setup(
    name="MediaBudgetOptimizer",
    version="3.0",
    description="媒体预算优化工具 v3.0 增强版",
    options={"build_exe": build_exe_options},
    executables=[exe]
)
