@echo off
chcp 65001 >nul
title 媒体预算优化工具 v3.0 增强版

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║            媒体预算优化工具 v3.0 增强版 (.exe模式)          ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🆕 v3.0 全新功能：
echo   ✅ KPI类型选择（BHT/Non-BHT）
echo   ✅ 16:9屏幕优化布局
echo   ✅ 实时监控仪表板
echo   ✅ 滑块配比设置
echo   ✅ 美化Excel输出
echo   ✅ 列字母输入
echo   ✅ 结果预览功能
echo.

REM 检查Python环境
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未检测到Python环境！
    echo.
    echo 💡 两种解决方案：
    echo 1. 安装Python 3.7+ 然后重新运行此文件
    echo 2. 或者运行 "媒体预算优化工具_v2.1_基础版.exe" （功能较少）
    echo.
    echo 📥 Python下载：https://www.python.org/downloads/
    echo ⚠️  安装时请勾选 "Add Python to PATH"
    echo.
    pause
    exit /b 1
)

echo ✅ Python环境检测通过
echo.

REM 检查依赖
echo 🔍 检查必要依赖...
python -c "import win32com.client" >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  缺少win32com库，正在自动安装...
    echo 📦 安装中，请稍候...
    python -m pip install pywin32
    if %errorlevel% neq 0 (
        echo ❌ 依赖安装失败！
        echo 💡 请手动运行：pip install pywin32
        echo 或者使用备用exe文件
        pause
        exit /b 1
    )
    echo ✅ 依赖安装成功
)

echo ✅ 依赖检查完成
echo.

REM 启动v3.0完整版
echo 🚀 启动媒体预算优化工具 v3.0 增强版...
echo 📊 加载所有新功能中...
echo.

python universal_media_optimizer_v3.py

if %errorlevel% neq 0 (
    echo.
    echo ❌ 程序启动失败！错误代码: %errorlevel%
    echo.
    echo 💡 故障排除：
    echo 1. 确保所有文件完整
    echo 2. 检查文件权限
    echo 3. 尝试以管理员身份运行
    echo 4. 或使用备用的v2.1版本
    echo.
    echo 🔍 详细错误信息请查看上方输出
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ 程序正常退出
echo 🎉 感谢使用媒体预算优化工具 v3.0！
pause
