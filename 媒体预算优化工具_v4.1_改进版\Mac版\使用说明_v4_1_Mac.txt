# 媒体预算优化工具 v4.1 - Mac版使用说明

## 🍎 Mac版本特点

### 跨平台兼容性
- **Excel后端**: 使用openpyxl替代Windows的win32com
- **界面适配**: 针对macOS界面风格优化
- **字体优化**: 使用Mac系统字体Arial
- **滚轮支持**: 完整的Mac滚轮事件支持

### 系统要求
- **操作系统**: macOS 10.14 或更高版本
- **Python**: 3.7+ (推荐使用Homebrew安装)
- **依赖库**: openpyxl, tkinter (通常随Python安装)
- **分辨率**: 1200x700 或更高(推荐16:9比例)

## 🚀 快速启动

### 方法一：脚本启动（推荐）
```bash
chmod +x start_v4_1_mac.sh
./start_v4_1_mac.sh
```

### 方法二：直接启动
```bash
python3 media_optimizer_v4_1_mac.py
```

### 方法三：双击启动
1. 在Finder中找到 `start_v4_1_mac.sh`
2. 右键选择"打开方式" > "终端"
3. 或者设置为可执行文件后双击

## 📦 安装依赖

### 使用Homebrew（推荐）
```bash
# 安装Python（如果未安装）
brew install python

# 安装依赖
pip3 install openpyxl
```

### 使用系统Python
```bash
# 安装依赖
pip3 install openpyxl
```

### 验证安装
```bash
python3 -c "import openpyxl; print('✅ openpyxl安装成功')"
python3 -c "import tkinter; print('✅ tkinter可用')"
```

## 🆕 v4.1 Mac版新功能

### 1. ✅ 16:9比例布局
- **网格系统**: 16列x9行的专业布局
- **合理分布**: 不再垂直叠放，水平垂直合理分配
- **Mac适配**: 针对Mac屏幕比例优化

### 2. ✅ 完整数据配置
- **数据范围**: 行列范围设置
- **渠道配置**: 渠道名称行设置
- **预算读取**: 预算读取列配置（新增）
- **KPI配置**: KPI读取行列范围（新增）

### 3. ✅ 简化输出配置
- **文件名设置**: 自定义输出文件名
- **路径选择**: Mac原生文件夹选择对话框
- **默认值**: 智能默认文件名

### 4. ✅ 手动基准比例
- **渠道管理**: 读取Excel中的渠道信息
- **手动设置**: 为每个渠道手动设置基准比例
- **自动标准化**: 一键标准化比例总和为100%

### 5. ✅ 完整优化功能
- **多线程优化**: 不阻塞界面的后台优化
- **实时监控**: 优化过程的实时进度显示
- **结果展示**: 详细的优化结果和方案对比

## 📊 使用流程

### 第一步：准备Excel文件
1. 确保Excel文件为.xlsx格式
2. 数据应在连续的行列范围内
3. 渠道名称应在指定行
4. KPI数据应在指定范围内

### 第二步：配置数据范围
1. **选择文件**: 点击"浏览"选择Excel文件
2. **选择工作表**: 从下拉列表选择工作表
3. **设置数据范围**: 
   - 数据行范围：如 2 到 10
   - 数据列范围：如 C 到 R
   - 渠道名称行：如 1
   - 预算读取列：如 B
4. **设置KPI范围**:
   - KPI行范围：如 2 到 10
   - KPI读取列：如 S

### 第三步：配置基准比例
1. **选择方式**:
   - 手动设置：为每个渠道手动输入比例
   - 数据表计算：从Excel指定范围计算
   - 平均分配：所有渠道平均分配
2. **手动设置**（推荐）:
   - 先点击"读取渠道信息"
   - 为每个渠道输入基准比例
   - 点击"标准化比例"确保总和为100%

### 第四步：配置优化参数
1. **总预算**: 输入总的预算金额
2. **KPI类型**: 选择Non-BHT（求和）或BHT（平均）
3. **生成方案数**: 设置要生成的方案数量（建议3-10个）
4. **迭代次数**: 设置优化迭代次数（建议100-500次）
5. **输出设置**: 设置输出文件名和保存路径

### 第五步：管理渠道
1. **读取渠道**: 点击"读取渠道信息"从Excel获取渠道列表
2. **设置类型**: 为每个渠道选择媒体类型
3. **设置约束**: 为每个渠道设置比例范围（如2%-40%）

### 第六步：开始优化
1. **启动优化**: 点击"开始优化"按钮
2. **监控进度**: 观察实时监控数据
3. **等待完成**: 优化过程会在后台运行

### 第七步：查看结果
1. **结果摘要**: 在结果区域查看优化摘要
2. **方案对比**: 查看多个优化方案的对比
3. **保存结果**: 结果会自动保存到指定位置

## 🔧 Mac特定注意事项

### 文件路径
- Mac使用正斜杠 `/` 作为路径分隔符
- 支持中文路径和文件名
- 建议将Excel文件放在用户目录下

### 权限设置
```bash
# 给启动脚本执行权限
chmod +x start_v4_1_mac.sh

# 如果遇到权限问题
sudo chmod +x start_v4_1_mac.sh
```

### Excel文件格式
- 优先使用.xlsx格式
- 确保文件未被其他程序占用
- 避免使用包含宏的.xlsm文件

### 性能优化
- 关闭不必要的后台应用
- 确保有足够的内存（建议8GB+）
- 使用SSD硬盘可提升文件读取速度

## ⚠️ 故障排除

### 常见问题

#### 1. Python未找到
```bash
# 安装Python
brew install python
# 或从python.org下载安装
```

#### 2. openpyxl导入失败
```bash
# 重新安装
pip3 uninstall openpyxl
pip3 install openpyxl
```

#### 3. tkinter不可用
```bash
# 安装tkinter
brew install python-tk
```

#### 4. 权限被拒绝
```bash
# 修改权限
chmod +x start_v4_1_mac.sh
```

#### 5. Excel文件读取失败
- 检查文件是否存在
- 确保文件未被占用
- 尝试重新保存为.xlsx格式

### 性能问题
- **优化缓慢**: 减少迭代次数或方案数
- **内存不足**: 关闭其他应用程序
- **界面卡顿**: 检查系统资源使用情况

## 📈 与Windows版本的差异

| 特性 | Windows版 | Mac版 |
|------|-----------|-------|
| Excel后端 | win32com | openpyxl |
| 字体 | Microsoft YaHei UI | Arial |
| 启动脚本 | .bat | .sh |
| 路径分隔符 | \ | / |
| 滚轮事件 | Windows标准 | Mac适配 |
| 文件对话框 | Windows原生 | Mac原生 |

## 🎯 最佳实践

### 数据准备
1. **标准化格式**: 确保数据格式一致
2. **清理数据**: 移除空行和无效数据
3. **备份文件**: 优化前备份原始文件

### 参数设置
1. **合理约束**: 设置合理的渠道比例范围
2. **适当迭代**: 根据数据复杂度设置迭代次数
3. **多方案对比**: 生成多个方案进行对比

### 结果验证
1. **检查总和**: 确保预算分配总和正确
2. **约束验证**: 验证结果是否满足约束条件
3. **业务合理性**: 从业务角度验证结果合理性

---

## 🎉 总结

**媒体预算优化工具v4.1 Mac版**提供了完整的跨平台解决方案：

- ✅ **完全兼容**: 原生Mac支持，无需Windows环境
- ✅ **功能完整**: 与Windows版本功能一致
- ✅ **界面优化**: 针对Mac界面风格优化
- ✅ **性能稳定**: 使用成熟的openpyxl库
- ✅ **易于使用**: 简单的安装和启动流程

**立即开始**: 运行 `./start_v4_1_mac.sh` 体验Mac版本！

**版本**: v4.1 Mac版  
**发布日期**: 2025年6月26日  
**状态**: ✅ 跨平台兼容  
**兼容性**: macOS 10.14+

🍎 **专为Mac用户优化的媒体预算优化解决方案！**
