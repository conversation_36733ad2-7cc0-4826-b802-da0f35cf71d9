#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Mac版媒体预算优化工具依赖安装脚本
自动安装所需的Python库
"""

import subprocess
import sys
import platform

def install_package(package):
    """安装Python包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ 成功安装: {package}")
        return True
    except subprocess.CalledProcessError:
        print(f"❌ 安装失败: {package}")
        return False

def main():
    print("🚀 Mac版媒体预算优化工具依赖安装器")
    print("=" * 50)
    print(f"系统信息: {platform.system()} {platform.release()}")
    print(f"Python版本: {sys.version}")
    print()
    
    # 需要安装的包
    packages = [
        "openpyxl",      # Excel文件处理
        "pandas",        # 数据处理（可选）
        "numpy",         # 数值计算（可选）
    ]
    
    print("正在安装依赖包...")
    success_count = 0
    
    for package in packages:
        print(f"安装 {package}...")
        if install_package(package):
            success_count += 1
    
    print()
    print("=" * 50)
    print(f"安装完成: {success_count}/{len(packages)} 个包安装成功")
    
    if success_count == len(packages):
        print("🎉 所有依赖安装成功！")
        print("现在可以运行: python universal_media_optimizer_mac.py")
    else:
        print("⚠️ 部分依赖安装失败，请手动安装")
        print("手动安装命令:")
        for package in packages:
            print(f"  pip install {package}")

if __name__ == "__main__":
    main()
