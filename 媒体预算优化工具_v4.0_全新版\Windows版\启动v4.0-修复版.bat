@echo off
title 媒体预算优化工具 v4.0 - 修复版

echo.
echo ========================================
echo    媒体预算优化工具 v4.0 - 修复版
echo ========================================
echo.
echo 正在启动程序...
echo.

REM 检查Python环境
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未检测到Python环境！
    echo.
    echo 解决方案：
    echo 1. 安装Python 3.7+
    echo 2. 安装时勾选 "Add Python to PATH"
    echo 3. 重新打开命令行窗口
    echo.
    pause
    exit /b 1
)

echo Python环境检测通过
echo.

REM 检查依赖
echo 检查必要依赖...
python -c "import win32com.client" >nul 2>&1
if %errorlevel% neq 0 (
    echo 缺少pywin32库，正在自动安装...
    python -m pip install pywin32
    if %errorlevel% neq 0 (
        echo 依赖安装失败！
        echo 请手动运行：pip install pywin32
        pause
        exit /b 1
    )
    echo pywin32安装成功
)

echo 依赖检查完成
echo.

REM 启动程序
echo 启动媒体预算优化工具 v4.0...
echo.

python universal_media_optimizer_v4.py

REM 程序退出后的处理
echo.
echo 程序已退出
echo 正在清理...

REM 清理Python进程
taskkill /f /im python.exe >nul 2>&1

echo.
echo 程序正常退出
echo 感谢使用媒体预算优化工具 v4.0！

timeout /t 3 /nobreak >nul
