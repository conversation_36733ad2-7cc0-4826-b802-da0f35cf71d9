#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版打包脚本
"""

import subprocess
import sys
import os

def main():
    print("🚀 开始打包...")
    
    # 检查文件
    if not os.path.exists("universal_media_optimizer_v2.py"):
        print("❌ 找不到主程序文件")
        return
    
    # 安装PyInstaller
    print("📦 安装PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✅ PyInstaller安装成功")
    except:
        print("⚠️ PyInstaller可能已安装")
    
    # 打包
    print("🔨 开始打包...")
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onefile",
        "--windowed", 
        "--name=媒体预算优化工具_v3.0_增强版",
        "universal_media_optimizer_v2.py"
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        print("STDOUT:", result.stdout)
        print("STDERR:", result.stderr)
        print("Return code:", result.returncode)
        
        if result.returncode == 0:
            print("✅ 打包成功！")
            if os.path.exists("dist"):
                print("📁 输出目录: dist/")
                for file in os.listdir("dist"):
                    print(f"  - {file}")
        else:
            print("❌ 打包失败")
            
    except subprocess.TimeoutExpired:
        print("⏰ 打包超时")
    except Exception as e:
        print(f"❌ 打包出错: {e}")

if __name__ == "__main__":
    main()
