#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
强制构建exe - 最后尝试
"""

import os
import sys
import subprocess
import shutil

def build_with_subprocess():
    """使用subprocess直接调用PyInstaller"""
    print("🔨 使用subprocess方法...")
    
    # 构建命令
    cmd = [
        sys.executable, "-c",
        """
import subprocess
import sys
cmd = [
    sys.executable, "-m", "PyInstaller",
    "--onefile",
    "--windowed", 
    "--name=MediaOptimizerV3",
    "universal_media_optimizer_v2.py"
]
subprocess.run(cmd)
"""
    ]
    
    try:
        result = subprocess.run(cmd, cwd=os.getcwd(), timeout=300)
        return result.returncode == 0
    except Exception as e:
        print(f"Subprocess方法失败: {e}")
        return False

def build_with_os_system():
    """使用os.system方法"""
    print("🔨 使用os.system方法...")
    
    cmd = f'"{sys.executable}" -m PyInstaller --onefile --windowed --name=MediaOptimizerV3 universal_media_optimizer_v2.py'
    
    try:
        result = os.system(cmd)
        return result == 0
    except Exception as e:
        print(f"os.system方法失败: {e}")
        return False

def build_with_direct_import():
    """直接导入PyInstaller模块"""
    print("🔨 使用直接导入方法...")
    
    try:
        import PyInstaller.__main__
        
        # 设置参数
        sys.argv = [
            'pyinstaller',
            '--onefile',
            '--windowed',
            '--name=MediaOptimizerV3',
            'universal_media_optimizer_v2.py'
        ]
        
        # 运行PyInstaller
        PyInstaller.__main__.run()
        return True
        
    except Exception as e:
        print(f"直接导入方法失败: {e}")
        return False

def main():
    print("🚀 强制构建exe文件 - 多种方法尝试")
    print("=" * 50)
    
    # 检查源文件
    if not os.path.exists("universal_media_optimizer_v2.py"):
        print("❌ 找不到源文件")
        return False
    
    # 清理旧文件
    if os.path.exists("dist"):
        shutil.rmtree("dist")
    if os.path.exists("build"):
        shutil.rmtree("build")
    
    # 尝试多种方法
    methods = [
        ("直接导入", build_with_direct_import),
        ("subprocess", build_with_subprocess),
        ("os.system", build_with_os_system)
    ]
    
    for method_name, method_func in methods:
        print(f"\n🔄 尝试方法: {method_name}")
        try:
            if method_func():
                # 检查是否生成了文件
                if os.path.exists("dist"):
                    files = os.listdir("dist")
                    if files:
                        print(f"✅ {method_name}方法成功!")
                        print(f"生成的文件: {files}")
                        return True
                print(f"⚠️ {method_name}方法执行完成但未找到输出文件")
            else:
                print(f"❌ {method_name}方法失败")
        except Exception as e:
            print(f"❌ {method_name}方法出错: {e}")
    
    print("\n❌ 所有方法都失败了")
    return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🎉 exe文件构建成功!")
        if os.path.exists("dist"):
            for file in os.listdir("dist"):
                file_path = os.path.join("dist", file)
                if os.path.isfile(file_path):
                    size_mb = os.path.getsize(file_path) / 1024 / 1024
                    print(f"📁 {file} ({size_mb:.1f} MB)")
    else:
        print("\n❌ 构建失败")
        print("💡 建议:")
        print("1. 检查PyInstaller是否正确安装")
        print("2. 尝试在命令行手动运行")
        print("3. 使用虚拟环境")
    
    input("按回车键退出...")
