# 媒体预算优化工具 v3.0 - Mac跨平台版

## 📋 概述

这是媒体预算优化工具的Mac跨平台版本，支持macOS、Linux和Windows系统。使用Python的tkinter和openpyxl库实现，无需依赖Microsoft Office。

## 🆕 v3.0 新功能

### 1. KPI类型选择
- **Non-BHT**: 各行KPI求和（传统方式）
- **BHT**: 各行KPI平均值（新增）

### 2. 16:9屏幕优化布局
- 左右分栏设计，适配宽屏显示器
- 左侧：配置区域
- 右侧：实时监控和结果预览

### 3. 实时监控仪表板
- 📈 基准KPI显示
- 🏆 当前最优KPI
- 📊 提升幅度
- ⚡ 优化状态
- 📝 实时日志

### 4. 结果预览功能
- 优化完成后先预览结果摘要
- 用户确认后再选择保存位置
- 美化的Excel输出格式

### 5. 滑块配比设置
- 直观的滑块界面设置自定义配比
- 实时显示总配比
- 智能预设（平均分配、智能分配等）

### 6. 列字母输入
- 支持Excel风格的列字母输入（A、B、C等）
- 自动转换数字和字母格式

### 7. 移除默认值
- 所有输入字段无默认值
- 用户需主动配置所有参数

## 💻 系统要求

- **操作系统**: macOS 10.12+, Ubuntu 18.04+, Windows 10+
- **Python**: 3.7+
- **内存**: 4GB+
- **存储**: 100MB+

## 🔧 安装步骤

### 方法1: 自动安装（推荐）

```bash
# 1. 下载文件
git clone <repository> 或下载ZIP文件

# 2. 进入目录
cd media-optimizer-mac

# 3. 运行安装脚本
python install_mac_dependencies.py

# 4. 启动应用
python universal_media_optimizer_mac.py
```

### 方法2: 手动安装

```bash
# 1. 安装依赖
pip install openpyxl pandas numpy

# 2. 启动应用
python universal_media_optimizer_mac.py
```

## 📖 使用指南

### 1. 基本配置
1. **选择Excel文件**: 点击"浏览"选择包含数据的Excel文件
2. **设置工作表**: 输入工作表名称（如"calculation"）
3. **配置数据区域**: 设置输入和输出的行列范围
4. **选择KPI类型**: Non-BHT（求和）或BHT（平均）

### 2. 读取渠道信息
1. 配置完成后点击"读取渠道信息"
2. 系统会自动从Excel中读取渠道名称
3. 在渠道信息区域查看读取结果

### 3. 设置预算和优化参数
1. **总预算**: 设置总的营销预算
2. **优化方案数量**: 生成的优化方案个数（建议3-6个）
3. **迭代次数**: 优化算法的迭代次数（建议200-500次）
4. **基准方案**: 选择平均分配或自定义配比

### 4. 开始优化
1. 点击"🚀 开始优化"按钮
2. 在右侧监控面板查看实时进度
3. 优化完成后在预览窗口查看结果摘要
4. 确认后选择保存位置

## 🔍 功能对比

| 功能 | Windows版 | Mac版 |
|------|-----------|-------|
| 基本优化算法 | ✅ 完整 | 🚧 开发中 |
| Excel读取 | ✅ win32com | ✅ openpyxl |
| 界面布局 | ✅ 16:9优化 | ✅ 16:9优化 |
| 实时监控 | ✅ 完整 | ✅ 完整 |
| 结果预览 | ✅ 完整 | ✅ 完整 |
| 滑块配比 | ✅ 完整 | 🚧 简化版 |
| Excel输出 | ✅ 美化版 | 🚧 开发中 |

## ⚠️ 注意事项

1. **当前状态**: Mac版本为预览版，核心优化算法仍在开发中
2. **完整功能**: 如需完整的优化功能，请使用Windows版本
3. **Excel格式**: 建议使用.xlsx格式的Excel文件
4. **权限**: 确保对Excel文件和输出目录有读写权限

## 🐛 故障排除

### 问题1: 提示缺少openpyxl库
```bash
# 解决方案
pip install openpyxl
```

### 问题2: 无法读取Excel文件
- 检查文件路径是否正确
- 确认工作表名称是否存在
- 验证文件是否被其他程序占用

### 问题3: 界面显示异常
- 确保使用Python 3.7+
- 检查tkinter是否正确安装
- 尝试调整窗口大小

## 🔄 版本历史

- **v3.0**: Mac跨平台版本，新增多项功能
- **v2.1**: Windows版界面美化
- **v2.0**: 基础优化功能

## 📞 技术支持

如遇问题，请提供以下信息：
- 操作系统版本
- Python版本
- 错误信息截图
- Excel文件格式

## 🚀 未来计划

- [ ] 完整的优化算法移植
- [ ] 更多预设配比模板
- [ ] 数据可视化图表
- [ ] 批量文件处理
- [ ] 云端协作功能

---

**开发团队**: Media Optimization Team  
**更新日期**: 2025年6月22日  
**版本**: v3.0 Mac Preview
