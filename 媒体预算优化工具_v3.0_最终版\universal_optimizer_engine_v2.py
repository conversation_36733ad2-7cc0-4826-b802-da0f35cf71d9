import win32com.client
import os
import time
import random
from datetime import datetime

class UniversalOptimizerEngineV2:
    def __init__(self, config, channels, progress_callback=None):
        self.config = config
        self.channels = channels
        self.progress_callback = progress_callback
        self.excel = None
        self.workbook = None
        self.worksheet = None

        # 控制状态
        self.is_paused = False
        self.is_terminated = False
        self.pause_event = None
        
        # 从配置中获取参数
        self.excel_file = config["excel_file"]
        self.worksheet_name = config["worksheet_name"]
        self.input_start_row = config["input_start_row"]
        self.input_end_row = config["input_end_row"]
        self.input_start_col = config["input_start_col"]
        self.input_end_col = config["input_end_col"]
        self.output_col = config["output_col"]
        self.output_start_row = config.get("output_start_row", config["input_start_row"])
        self.output_end_row = config.get("output_end_row", config["input_end_row"])
        self.total_budget = config["total_budget"]
        self.optimization_schemes = config["optimization_schemes"]
        self.baseline_allocation = config["baseline_allocation"]
        self.custom_baseline_ratios = config.get("custom_baseline_ratios", {})
        self.channel_constraints = config.get("channel_constraints", {})

        # 默认约束范围：2%-60%
        self.default_min_ratio = 0.02
        self.default_max_ratio = 0.60

        # 调试信息：显示关键配置参数
        print(f"🔧 优化器初始化:")
        print(f"   总预算: ¥{self.total_budget:,}")
        print(f"   方案数量: {self.optimization_schemes}")
        print(f"   迭代次数: {config.get('max_iterations', 200)}")
        print(f"   渠道数量: {len(self.channels)}")
        print(f"   基准分配: {self.baseline_allocation}")

    def pause_optimization(self):
        """暂停优化"""
        self.is_paused = True
        if self.progress_callback:
            self.progress_callback(-1, "已暂停", "优化已暂停，点击恢复继续")

    def resume_optimization(self):
        """恢复优化"""
        self.is_paused = False
        if self.progress_callback:
            self.progress_callback(-1, "已恢复", "优化已恢复")

    def terminate_optimization(self):
        """终止优化"""
        self.is_terminated = True
        if self.progress_callback:
            self.progress_callback(-1, "已终止", "优化已被用户终止")

    def check_control_state(self):
        """检查控制状态"""
        # 检查是否被终止
        if self.is_terminated:
            return "terminated"

        # 检查是否暂停
        while self.is_paused and not self.is_terminated:
            time.sleep(0.1)  # 暂停时等待

        return "running" if not self.is_terminated else "terminated"

    def generate_efficiency_based_weights(self, channel_performance):
        """基于渠道效率生成权重"""
        weights = []
        for channel in self.channels:
            perf = next((p for p in channel_performance if p['channel'] == channel), None)
            if perf and perf['improvement'] > 0:
                # 根据效率排名分配权重
                rank = channel_performance.index(perf) + 1
                if rank <= 3:
                    weight = random.uniform(2.0, 5.0)  # 高效渠道
                elif rank <= 8:
                    weight = random.uniform(1.0, 3.0)  # 中效渠道
                else:
                    weight = random.uniform(0.2, 1.5)  # 低效渠道
            else:
                weight = random.uniform(0.1, 0.8)  # 负效率渠道
            weights.append(weight)
        return weights

    def generate_concentrated_weights(self, channel_performance):
        """生成极端集中的权重分配（80/20原则）"""
        weights = [0.1] * len(self.channels)  # 基础权重

        # 选择前20%的高效渠道
        top_count = max(1, len(self.channels) // 5)
        top_channels = channel_performance[:top_count]

        for i, channel in enumerate(self.channels):
            if any(perf['channel'] == channel for perf in top_channels):
                weights[i] = random.uniform(5.0, 10.0)  # 极高权重

        return weights

    def generate_progressive_weights(self, channel_performance, iteration, max_iterations):
        """生成渐进式权重（随迭代逐步集中）"""
        progress = iteration / max_iterations

        weights = []
        for channel in self.channels:
            perf = next((p for p in channel_performance if p['channel'] == channel), None)
            if perf and perf['improvement'] > 0:
                rank = channel_performance.index(perf) + 1
                # 随着迭代进行，逐步集中到高效渠道
                base_weight = 3.0 - (rank - 1) * 0.3
                concentration_factor = 1 + progress * 2  # 逐步增加集中度
                weight = base_weight * concentration_factor * random.uniform(0.8, 1.2)
            else:
                weight = random.uniform(0.1, 0.5) * (1 - progress * 0.5)  # 逐步减少低效渠道
            weights.append(max(0.05, weight))  # 确保最小权重

        return weights

    def generate_random_exploration_weights(self):
        """生成随机探索权重"""
        return [random.uniform(0.1, 4.0) for _ in self.channels]

    def generate_hybrid_weights(self, channel_performance, iteration):
        """生成混合策略权重"""
        # 结合效率和随机性
        efficiency_weights = self.generate_efficiency_based_weights(channel_performance)
        random_weights = self.generate_random_exploration_weights()

        # 根据迭代次数调整混合比例
        efficiency_ratio = 0.7 + (iteration % 100) * 0.003  # 70%-100%效率权重
        random_ratio = 1 - efficiency_ratio

        weights = []
        for i in range(len(self.channels)):
            weight = efficiency_weights[i] * efficiency_ratio + random_weights[i] * random_ratio
            weights.append(weight)

        return weights

    def simulate_kpi_calculation(self, budgets, baseline_kpi, channel_performance):
        """模拟KPI计算（用于测试环境）"""
        total_improvement = 0
        base_budget_per_channel = self.total_budget / len(self.channels)

        for i, budget in enumerate(budgets):
            channel = self.channels[i]
            perf = next((p for p in channel_performance if p['channel'] == channel), None)

            if perf:
                # 计算预算变化比例
                budget_ratio = budget / base_budget_per_channel
                # 基于渠道效率和预算变化估算KPI贡献
                channel_contribution = perf['improvement'] * (budget_ratio - 1) * 0.5
                total_improvement += channel_contribution

        simulated_kpi = baseline_kpi + total_improvement
        return simulated_kpi, budgets

    def generate_differentiated_schemes(self, baseline_kpi, channel_performance):
        """生成差异化投放方案（基于渠道类别）"""
        differentiated_results = []

        # 获取渠道分类
        channel_classification, _ = self.get_channel_categories()

        # 定义差异化投放策略（确保每种渠道类别都有重点方案）
        strategies = []

        # 根据实际渠道分类动态生成策略
        available_categories = [cat for cat, channels in channel_classification.items() if channels]

        # 策略1: 数字媒体重点方案
        if any(cat in available_categories for cat in ["数字媒体", "社交媒体"]):
            strategies.append({
                "name": "数字营销重点方案",
                "focus_categories": ["数字媒体", "社交媒体"],
                "focus_ratio": 0.65,
                "target_improvement": 0.08,  # 目标8%提升
                "description": "重点投放数字媒体和社交平台"
            })

        # 策略2: 社交媒体专注方案
        if "社交媒体" in available_categories:
            strategies.append({
                "name": "社交互动专注方案",
                "focus_categories": ["社交媒体"],
                "focus_ratio": 0.55,
                "target_improvement": 0.06,  # 目标6%提升
                "description": "专注社交媒体和KOL营销"
            })

        # 策略3: 视频广告重点方案
        if "视频广告" in available_categories:
            strategies.append({
                "name": "视频广告重点方案",
                "focus_categories": ["视频广告"],
                "focus_ratio": 0.45,
                "target_improvement": 0.04,  # 目标4%提升
                "description": "重点投放视频广告渠道"
            })

        # 策略4: 线下媒体重点方案
        if "线下媒体" in available_categories:
            strategies.append({
                "name": "线下媒体重点方案",
                "focus_categories": ["线下媒体"],
                "focus_ratio": 0.40,
                "target_improvement": 0.03,  # 目标3%提升
                "description": "重点投放线下媒体渠道"
            })

        # 策略5: 赞助营销重点方案
        if "赞助营销" in available_categories:
            strategies.append({
                "name": "赞助营销重点方案",
                "focus_categories": ["赞助营销"],
                "focus_ratio": 0.35,
                "target_improvement": 0.025,  # 目标2.5%提升
                "description": "重点投放赞助营销渠道"
            })

        # 策略6: 电商平台重点方案
        if "电商平台" in available_categories:
            strategies.append({
                "name": "电商平台重点方案",
                "focus_categories": ["电商平台"],
                "focus_ratio": 0.50,
                "target_improvement": 0.05,  # 目标5%提升
                "description": "重点投放电商平台渠道"
            })

        # 策略7: 内容营销重点方案
        if "内容营销" in available_categories:
            strategies.append({
                "name": "内容营销重点方案",
                "focus_categories": ["内容营销"],
                "focus_ratio": 0.40,
                "target_improvement": 0.035,  # 目标3.5%提升
                "description": "重点投放内容营销渠道"
            })

        # 策略8: 移动应用重点方案
        if "移动应用" in available_categories:
            strategies.append({
                "name": "移动应用重点方案",
                "focus_categories": ["移动应用"],
                "focus_ratio": 0.45,
                "target_improvement": 0.04,  # 目标4%提升
                "description": "重点投放移动应用渠道"
            })

        # 如果没有足够的策略，添加均衡方案
        if len(strategies) < 3:
            strategies.append({
                "name": "全渠道均衡方案",
                "focus_categories": [],
                "focus_ratio": 0.0,
                "target_improvement": 0.02,  # 目标2%提升
                "description": "各类型媒体均衡投放"
            })

        # 按目标提升幅度排序策略（从小到大，确保递进）
        strategies.sort(key=lambda x: x.get("target_improvement", 0))

        # 限制策略数量，确保不超过配置的方案数
        max_schemes = min(len(strategies), self.optimization_schemes - 1)  # 减1为基准方案留位置
        selected_strategies = strategies[:max_schemes]

        return self.generate_category_based_schemes(baseline_kpi, channel_performance, channel_classification, selected_strategies)

    def generate_category_based_schemes(self, baseline_kpi, channel_performance, channel_classification, strategies):
        """基于渠道类别生成差异化方案"""
        results = []

        for strategy in strategies:
            # 生成基于策略的预算分配
            budgets = self.allocate_budget_by_strategy(strategy, channel_classification, channel_performance)

            # 计算KPI
            if hasattr(self, 'worksheet') and self.worksheet:
                kpi, final_budgets = self.write_budgets_and_calculate(budgets)
            else:
                kpi, final_budgets = self.simulate_kpi_calculation(budgets, baseline_kpi, channel_performance)

            improvement = kpi - baseline_kpi
            improvement_pct = (improvement / baseline_kpi) * 100 if baseline_kpi > 0 else 0

            # 只保存正提升的方案
            if improvement > 0:
                final_budget_dict = dict(zip(self.channels, final_budgets))
                scheme_desc = self.generate_scheme_description(strategy["name"], final_budget_dict)

                results.append({
                    "方案名称": strategy["name"],
                    "方案描述": scheme_desc,
                    "KPI": kpi,
                    "预算分配": final_budget_dict,
                    "提升": improvement,
                    "提升比例": f"{improvement_pct:.2f}%",
                    "策略类型": strategy["focus_categories"]
                })

        return results

    def allocate_budget_by_strategy(self, strategy, channel_classification, channel_performance):
        """根据策略分配预算（优化版，确保达到目标提升）"""
        budgets = [0.0] * len(self.channels)
        target_improvement = strategy.get("target_improvement", 0.02)

        if strategy["focus_categories"]:
            # 有重点类别的策略
            focus_channels = []
            other_channels = []

            # 分类渠道
            for channel in self.channels:
                is_focus = False
                for category, channels_in_cat in channel_classification.items():
                    if channel in channels_in_cat and category in strategy["focus_categories"]:
                        focus_channels.append(channel)
                        is_focus = True
                        break
                if not is_focus:
                    other_channels.append(channel)

            # 分配预算 - 根据目标提升调整强度
            if focus_channels:
                # 基础重点比例
                base_focus_ratio = strategy["focus_ratio"]

                # 根据目标提升调整重点比例（提升越高，集中度越高）
                adjustment_factor = min(target_improvement / 0.05, 1.5)  # 最大1.5倍调整
                adjusted_focus_ratio = min(base_focus_ratio * adjustment_factor, 0.8)  # 最高80%

                focus_budget_total = self.total_budget * adjusted_focus_ratio
                other_budget_total = self.total_budget * (1 - adjusted_focus_ratio)

                # 重点渠道内部按效率分配，但增加差异化
                focus_weights = self.get_enhanced_channel_weights(focus_channels, channel_performance, target_improvement)
                focus_budgets = self.distribute_budget_by_weights(focus_budget_total, focus_weights)

                # 其他渠道按效率分配（而非平均分配）
                if other_channels:
                    other_weights = self.get_channel_weights(other_channels, channel_performance)
                    other_budgets = self.distribute_budget_by_weights(other_budget_total, other_weights)
                else:
                    other_budgets = []

                # 分配到具体渠道
                for i, channel in enumerate(self.channels):
                    if channel in focus_channels:
                        focus_index = focus_channels.index(channel)
                        budgets[i] = focus_budgets[focus_index]
                    elif channel in other_channels:
                        other_index = other_channels.index(channel)
                        budgets[i] = other_budgets[other_index]
                    else:
                        budgets[i] = 0
            else:
                # 如果没有重点渠道，平均分配
                budgets = [self.total_budget / len(self.channels)] * len(self.channels)
        else:
            # 均衡策略：按渠道效率适度调整
            base_budget = self.total_budget / len(self.channels)
            adjustment_strength = target_improvement * 10  # 调整强度

            for i, channel in enumerate(self.channels):
                perf = next((p for p in channel_performance if p['channel'] == channel), None)
                if perf:
                    rank = channel_performance.index(perf) + 1
                    total_channels = len(self.channels)

                    if rank <= total_channels // 3:
                        multiplier = 1.0 + adjustment_strength * 0.5  # 高效渠道增加
                    elif rank <= total_channels * 2 // 3:
                        multiplier = 1.0  # 中效渠道保持
                    else:
                        multiplier = 1.0 - adjustment_strength * 0.3  # 低效渠道减少

                    budgets[i] = base_budget * multiplier
                else:
                    budgets[i] = base_budget * (1.0 - adjustment_strength * 0.2)

            # 重新归一化
            total_check = sum(budgets)
            budgets = [b * self.total_budget / total_check for b in budgets]

        return budgets

    def get_channel_weights(self, channels, channel_performance):
        """获取渠道权重"""
        weights = []
        for channel in channels:
            perf = next((p for p in channel_performance if p['channel'] == channel), None)
            if perf and perf['improvement'] > 0:
                # 基于效率的权重
                rank = channel_performance.index(perf) + 1
                weight = max(0.1, 2.0 - (rank - 1) * 0.1)  # 排名越高权重越大
            else:
                weight = 0.1  # 最小权重
            weights.append(weight)
        return weights

    def get_enhanced_channel_weights(self, channels, channel_performance, target_improvement):
        """获取增强的渠道权重（根据目标提升调整）"""
        weights = []
        enhancement_factor = target_improvement * 20  # 增强因子

        for channel in channels:
            perf = next((p for p in channel_performance if p['channel'] == channel), None)
            if perf and perf['improvement'] > 0:
                # 基于效率的基础权重
                rank = channel_performance.index(perf) + 1
                base_weight = max(0.1, 2.0 - (rank - 1) * 0.1)

                # 根据目标提升增强权重差异
                if rank <= 3:  # 前3名渠道
                    enhanced_weight = base_weight * (1 + enhancement_factor)
                elif rank <= 6:  # 中等渠道
                    enhanced_weight = base_weight * (1 + enhancement_factor * 0.5)
                else:  # 低效渠道
                    enhanced_weight = base_weight * (1 - enhancement_factor * 0.3)

                weight = max(0.05, enhanced_weight)  # 确保最小权重
            else:
                weight = 0.05  # 负效率渠道最小权重
            weights.append(weight)
        return weights

    def distribute_budget_by_weights(self, total_budget, weights):
        """按权重分配预算"""
        total_weight = sum(weights)
        if total_weight > 0:
            return [total_budget * w / total_weight for w in weights]
        else:
            return [total_budget / len(weights)] * len(weights)

    def initialize_excel(self):
        """初始化Excel连接"""
        try:
            # 先尝试关闭可能存在的Excel进程
            try:
                os.system("taskkill /f /im excel.exe >nul 2>&1")
                time.sleep(1)
            except:
                pass

            # 创建新的Excel应用实例
            self.excel = win32com.client.Dispatch("Excel.Application")

            # 设置Excel属性（分步设置，便于调试）
            try:
                self.excel.DisplayAlerts = False
                self.excel.ScreenUpdating = False
                self.excel.Visible = False
            except Exception as e:
                print(f"设置Excel属性时出现警告: {e}")
                # 继续执行，这些属性设置失败通常不影响核心功能

            # 打开工作簿
            self.workbook = self.excel.Workbooks.Open(os.path.abspath(self.excel_file))
            self.worksheet = self.workbook.Worksheets(self.worksheet_name)

            print(f"Excel连接成功，工作表: {self.worksheet_name}")
            return True

        except Exception as e:
            print(f"初始化Excel失败: {e}")
            print("可能的解决方案:")
            print("1. 确保Excel文件未被其他程序占用")
            print("2. 检查文件路径是否正确")
            print("3. 确保Excel已正确安装")
            return False
    
    def cleanup_excel(self):
        """清理Excel资源"""
        try:
            if self.workbook:
                self.workbook.Close(SaveChanges=False)
            if self.excel:
                self.excel.ScreenUpdating = True
                self.excel.Quit()
        except:
            pass
        finally:
            try:
                os.system("taskkill /f /im excel.exe >nul 2>&1")
            except:
                pass
    
    def apply_constraints(self, budgets):
        """应用渠道约束"""
        constrained_budgets = budgets.copy()
        
        for i, channel in enumerate(self.channels):
            if channel in self.channel_constraints:
                constraint = self.channel_constraints[channel]
                min_ratio = constraint["min"]
                max_ratio = constraint["max"]
            else:
                # 使用默认约束
                min_ratio = self.default_min_ratio
                max_ratio = self.default_max_ratio
            
            min_budget = self.total_budget * min_ratio
            max_budget = self.total_budget * max_ratio
            
            constrained_budgets[i] = max(min_budget, min(max_budget, constrained_budgets[i]))
        
        # 重新归一化到总预算
        total_constrained = sum(constrained_budgets)
        if total_constrained > 0:
            constrained_budgets = [b * self.total_budget / total_constrained for b in constrained_budgets]
        
        return constrained_budgets

    def get_constraint_summary(self):
        """获取约束设置摘要"""
        if not self.channel_constraints:
            return f"默认约束 {self.default_min_ratio:.1%}-{self.default_max_ratio:.1%} 应用于所有渠道"

        custom_count = len(self.channel_constraints)
        default_count = len(self.channels) - custom_count

        if custom_count == 0:
            return f"默认约束 {self.default_min_ratio:.1%}-{self.default_max_ratio:.1%} 应用于所有{len(self.channels)}个渠道"
        elif default_count == 0:
            return f"所有{len(self.channels)}个渠道使用自定义约束"
        else:
            return f"{custom_count}个渠道使用自定义约束，{default_count}个渠道使用默认约束({self.default_min_ratio:.1%}-{self.default_max_ratio:.1%})"

    def get_channel_categories(self):
        """获取渠道分类（可适配不同项目，支持中英文）"""
        # 通用渠道分类规则（基于关键词匹配，支持中英文）
        categories = {
            "数字媒体": {
                "keywords": [
                    # 英文关键词
                    "Search", "Display", "RTB", "Digital", "SEM", "SEO", "Programmatic", "Google", "Baidu", "Bing",
                    # 中文关键词
                    "搜索", "展示", "程序化", "数字", "竞价", "广告联盟", "DSP", "百度", "谷歌"
                ],
                "description": "精准投放，数据驱动"
            },
            "社交媒体": {
                "keywords": [
                    # 英文关键词
                    "KOL", "Social", "Weibo", "Douyin", "Red", "WeChat", "TikTok", "Instagram", "Facebook",
                    "Twitter", "LinkedIn", "Snapchat", "Pinterest", "Influencer",
                    # 中文关键词
                    "社交", "微博", "抖音", "小红书", "微信", "快手", "B站", "知乎", "达人", "网红", "博主"
                ],
                "description": "社交互动，口碑传播"
            },
            "视频广告": {
                "keywords": [
                    # 英文关键词
                    "OTT", "OTV", "Video", "Preroll", "YouTube", "iQiyi", "Youku", "TV", "Television", "Streaming",
                    # 中文关键词
                    "视频", "电视", "贴片", "爱奇艺", "优酷", "腾讯视频", "芒果TV", "OTT", "联网电视"
                ],
                "description": "视觉冲击，品牌故事"
            },
            "线下媒体": {
                "keywords": [
                    # 英文关键词
                    "LCD", "Metro", "Outdoor", "Billboard", "Transit", "Cinema", "Radio", "Print", "Magazine", "Newspaper",
                    # 中文关键词
                    "线下", "户外", "地铁", "公交", "电梯", "楼宇", "广播", "报纸", "杂志", "影院", "LED", "灯箱"
                ],
                "description": "广泛覆盖，品牌曝光"
            },
            "赞助营销": {
                "keywords": [
                    # 英文关键词
                    "Sponsor", "Event", "Drama", "Sports", "Entertainment", "Partnership", "Show", "Concert",
                    # 中文关键词
                    "赞助", "活动", "电视剧", "综艺", "体育", "娱乐", "演出", "音乐会", "冠名", "植入"
                ],
                "description": "品牌联想，情感连接"
            },
            "电商平台": {
                "keywords": [
                    # 英文关键词
                    "Tmall", "JD", "Taobao", "Amazon", "Ecommerce", "Shop", "Mall", "Store", "Marketplace",
                    # 中文关键词
                    "天猫", "京东", "淘宝", "拼多多", "苏宁", "唯品会", "电商", "商城", "店铺", "平台"
                ],
                "description": "直接转化，销售驱动"
            },
            "内容营销": {
                "keywords": [
                    # 英文关键词
                    "Content", "Native", "Article", "Blog", "News", "Editorial", "Advertorial",
                    # 中文关键词
                    "内容", "原生", "文章", "博客", "新闻", "软文", "资讯", "自媒体"
                ],
                "description": "软性植入，价值传递"
            },
            "移动应用": {
                "keywords": [
                    # 英文关键词
                    "App", "Mobile", "In-App", "Game", "Push", "iOS", "Android",
                    # 中文关键词
                    "应用", "手机", "移动", "游戏", "推送", "安卓", "苹果"
                ],
                "description": "移动优先，即时触达"
            }
        }

        # 根据实际渠道名称进行分类
        channel_classification = {}
        unclassified_channels = []

        for channel in self.channels:
            classified = False
            # 尝试匹配每个分类
            for category, info in categories.items():
                for keyword in info["keywords"]:
                    # 支持中英文匹配，忽略大小写
                    if keyword.lower() in channel.lower():
                        if category not in channel_classification:
                            channel_classification[category] = []
                        channel_classification[category].append(channel)
                        classified = True
                        break
                if classified:
                    break

            # 记录未分类的渠道
            if not classified:
                unclassified_channels.append(channel)

        # 将未分类的渠道归入"其他"
        if unclassified_channels:
            channel_classification["其他"] = unclassified_channels
            # 为"其他"类别添加描述
            categories["其他"] = {"description": "其他类型媒体"}

        return channel_classification, categories

    def generate_scheme_description(self, scheme_name, budget_allocation):
        """根据预算分配生成方案描述（带错误处理）"""
        try:
            channel_classification, _ = self.get_channel_categories()

            # 计算各类型渠道的预算占比
            category_ratios = {}
            for category, channels_in_cat in channel_classification.items():
                total_budget_in_cat = sum(budget_allocation.get(ch, 0) for ch in channels_in_cat)
                ratio = total_budget_in_cat / self.total_budget if self.total_budget > 0 else 0
                category_ratios[category] = ratio

            # 按预算占比排序
            sorted_categories = sorted(category_ratios.items(), key=lambda x: x[1], reverse=True)

            # 生成描述
            if "基准方案" in scheme_name:
                if "平均分配" in scheme_name:
                    return "所有渠道平均分配预算，均衡覆盖各媒体类型，适合品牌认知建设阶段"
                else:
                    return "基于自定义配比分配预算，体现特定媒体策略偏好"
        except Exception as e:
            print(f"生成方案描述时出错: {e}")
            # 返回默认描述
            if "基准方案" in scheme_name:
                return "基准方案，作为对比基础"
            else:
                return "优化方案，基于算法搜索得到的预算分配"

        try:
            # 分析主要投放策略
            top_categories = [cat for cat, ratio in sorted_categories[:3] if ratio > 0.15]  # 占比超过15%的主要类型

            description_parts = []

            # 主要策略描述
            if len(top_categories) >= 2:
                main_cats = top_categories[:2]
                main_ratios = [category_ratios[cat] for cat in main_cats]

                if "数字媒体" in main_cats and "社交媒体" in main_cats:
                    description_parts.append("数字化营销导向")
                    if sum(main_ratios) > 0.6:
                        description_parts.append("重点布局数字媒体和社交平台")
                    else:
                        description_parts.append("平衡数字媒体和社交互动")

                elif "线下媒体" in main_cats or "视频广告" in main_cats:
                    if category_ratios.get("线下媒体", 0) + category_ratios.get("视频广告", 0) > 0.4:
                        description_parts.append("传统媒体主导")
                        description_parts.append("注重品牌曝光和覆盖面")
                    else:
                        description_parts.append("传统与数字媒体并重")

                elif "赞助营销" in main_cats:
                    description_parts.append("品牌营销导向")
                    description_parts.append("通过赞助活动建立品牌联想")

                elif "电商平台" in main_cats:
                    description_parts.append("电商转化导向")
                    description_parts.append("重点布局电商平台和销售转化")
        except Exception as e:
            print(f"分析投放策略时出错: {e}")
            description_parts = ["多元化媒体组合"]

        try:
            # 具体渠道特色
            top_channels = sorted(budget_allocation.items(), key=lambda x: x[1], reverse=True)[:3]
            high_budget_channels = [ch for ch, budget in top_channels if budget / self.total_budget > 0.2]

            if high_budget_channels:
                # 通用渠道描述映射（支持中英文）
                channel_descriptions = {
                    # 中文渠道
                    "SearchVolume": "搜索营销", "DouyinKOL": "抖音KOL", "RedKOL": "小红书KOL",
                    "WeiboKOL": "微博KOL", "OTTPreroll": "OTT视频", "Display": "展示广告",
                    "RTBOCPX": "程序化购买", "DigitalCoop": "数字合作",
                    # 英文渠道
                    "GoogleAds": "Google广告", "FacebookAds": "Facebook广告", "InstagramKOL": "Instagram网红",
                    "YouTubeVideo": "YouTube视频", "TikTokAds": "TikTok广告", "LinkedInAds": "LinkedIn广告",
                    "TmallSearch": "天猫搜索", "JDDisplay": "京东展示", "AmazonSponsor": "亚马逊赞助"
                }

                featured_channels = []
                for ch in high_budget_channels:
                    if ch in channel_descriptions:
                        featured_channels.append(channel_descriptions[ch])
                    else:
                        # 如果没有预定义描述，直接使用渠道名
                        featured_channels.append(ch)

                if featured_channels:
                    description_parts.append(f"重点投放{'/'.join(featured_channels)}")

            # 投放目标描述
            digital_ratio = category_ratios.get("数字媒体", 0) + category_ratios.get("社交媒体", 0)
            traditional_ratio = category_ratios.get("线下媒体", 0) + category_ratios.get("视频广告", 0)
            ecommerce_ratio = category_ratios.get("电商平台", 0)

            if digital_ratio > 0.7:
                description_parts.append("追求精准触达和效果转化")
            elif traditional_ratio > 0.4:
                description_parts.append("强化品牌认知和市场覆盖")
            elif ecommerce_ratio > 0.3:
                description_parts.append("注重销售转化和电商增长")
            elif digital_ratio > 0.4 and traditional_ratio > 0.2:
                description_parts.append("兼顾品牌建设与效果转化")

        except Exception as e:
            print(f"分析渠道特色时出错: {e}")
            if not description_parts:
                description_parts = ["多元化媒体组合"]

        # 组合描述
        try:
            if description_parts:
                return "，".join(description_parts)
            else:
                return "多元化媒体组合，平衡各类型渠道投入"
        except Exception as e:
            print(f"组合描述时出错: {e}")
            return "优化方案，基于数据驱动的预算分配"
    
    def write_budgets_and_calculate(self, budgets):
        """写入预算并计算KPI"""
        try:
            # 应用约束
            constrained_budgets = self.apply_constraints(budgets)
            
            # 清空输入区域
            input_range = self.worksheet.Range(
                self.worksheet.Cells(self.input_start_row, self.input_start_col),
                self.worksheet.Cells(self.input_end_row, self.input_end_col)
            )
            input_range.ClearContents()
            
            # 计算每行每个渠道的预算
            num_rows = self.input_end_row - self.input_start_row + 1
            budgets_per_row = [budget / num_rows for budget in constrained_budgets]
            
            # 写入预算数据
            for row in range(self.input_start_row, self.input_end_row + 1):
                for i, budget_per_row in enumerate(budgets_per_row):
                    col = self.input_start_col + i
                    self.worksheet.Cells(row, col).Value = budget_per_row
            
            # 强制重新计算
            self.worksheet.Calculate()
            time.sleep(0.3)
            
            # 读取输出值并根据KPI类型计算
            kpi_values = []
            for row in range(self.output_start_row, self.output_end_row + 1):
                kpi_value = self.worksheet.Cells(row, self.output_col).Value
                if kpi_value is not None:
                    kpi_values.append(float(kpi_value))

            # 根据KPI类型计算最终KPI
            kpi_type = self.config.get("kpi_type", "Non-BHT")
            if kpi_type == "BHT":
                # BHT类型：计算平均值
                total_kpi = sum(kpi_values) / len(kpi_values) if kpi_values else 0
            else:
                # Non-BHT类型：计算求和（默认）
                total_kpi = sum(kpi_values)

            return total_kpi, constrained_budgets
        except Exception as e:
            print(f"计算KPI时出错: {e}")
            return 0, budgets
    
    def get_baseline_kpi(self):
        """获取基准方案的KPI"""
        if self.baseline_allocation == "equal":
            # 平均分配
            equal_budgets = [self.total_budget / len(self.channels)] * len(self.channels)
        elif self.baseline_allocation == "custom":
            # 自定义基准方案
            if not self.custom_baseline_ratios:
                raise ValueError("自定义基准配比未设置")
            
            equal_budgets = []
            for channel in self.channels:
                if channel in self.custom_baseline_ratios:
                    ratio = self.custom_baseline_ratios[channel] / 100.0  # 转换为小数
                    equal_budgets.append(self.total_budget * ratio)
                else:
                    # 如果某个渠道没有设置，使用平均值
                    equal_budgets.append(self.total_budget / len(self.channels))
        else:
            # 默认平均分配
            equal_budgets = [self.total_budget / len(self.channels)] * len(self.channels)
        
        baseline_kpi, constrained_baseline = self.write_budgets_and_calculate(equal_budgets)
        return baseline_kpi, constrained_baseline
    
    def test_individual_channels(self, baseline_kpi):
        """测试各渠道单独表现"""
        channel_performance = []
        
        for i, channel in enumerate(self.channels):
            # 给该渠道分配30%预算，其他渠道平分剩余70%
            test_budgets = [self.total_budget * 0.7 / (len(self.channels) - 1)] * len(self.channels)
            test_budgets[i] = self.total_budget * 0.3
            
            kpi, _ = self.write_budgets_and_calculate(test_budgets)
            improvement = kpi - baseline_kpi
            
            channel_performance.append({
                'channel': channel,
                'index': i,
                'kpi': kpi,
                'improvement': improvement,
                'efficiency': improvement / (self.total_budget * 0.3)
            })
        
        # 按改进效果排序
        channel_performance.sort(key=lambda x: x['improvement'], reverse=True)
        return channel_performance
    
    def generate_business_description(self, budgets, scheme_type="custom"):
        """根据预算分配生成业务导向的方案描述"""
        # 定义渠道分类
        channel_categories = {
            "数字媒体": ["SearchVolume", "Display", "RTBOCPX", "DigitalCoop"],
            "社交媒体": ["DouyinKOL", "RedKOL", "WeiboKOL", "DouyinPush", "WeiboPush"],
            "视频广告": ["OTTPreroll", "OTVPreroll"],
            "线下媒体": ["BuildingLCD", "Metro", "CreativeOutdoor"],
            "赞助营销": ["SponsorEvent", "SponsorDrama"]
        }

        # 计算各类型渠道的预算占比
        category_ratios = {}
        for category, channels_in_cat in channel_categories.items():
            total_budget_in_cat = sum(budgets[self.channels.index(ch)] for ch in channels_in_cat if ch in self.channels)
            ratio = total_budget_in_cat / self.total_budget
            category_ratios[category] = ratio

        # 计算数字化比例
        digital_ratio = category_ratios.get("数字媒体", 0) + category_ratios.get("社交媒体", 0)

        # 找出主导渠道类型
        dominant_category = max(category_ratios.items(), key=lambda x: x[1])

        # 生成业务描述
        if scheme_type == "baseline_equal":
            return "均衡配置策略，各渠道平均分配预算，适合品牌初期测试和全渠道覆盖"
        elif scheme_type == "baseline_custom":
            return "定制化基准策略，基于历史经验和品牌特点的预算配比方案"

        # 根据数字化比例生成描述
        if digital_ratio >= 0.7:
            digital_desc = "数字化优先策略"
            focus_desc = "重点投放数字媒体和社交平台，适合年轻消费群体和电商转化目标"
        elif digital_ratio >= 0.5:
            digital_desc = "数字化主导策略"
            focus_desc = "平衡数字媒体与传统媒体，兼顾品牌曝光和精准转化"
        elif digital_ratio >= 0.3:
            digital_desc = "传统数字并重策略"
            focus_desc = "传统媒体为主，数字媒体补充，适合成熟品牌和大众市场"
        else:
            digital_desc = "传统媒体主导策略"
            focus_desc = "以传统媒体为核心，强化品牌认知和大众覆盖"

        # 根据主导渠道类型添加细节
        if dominant_category[0] == "社交媒体" and dominant_category[1] > 0.4:
            detail_desc = "，强化KOL合作和社交互动，提升品牌话题度和用户参与"
        elif dominant_category[0] == "数字媒体" and dominant_category[1] > 0.3:
            detail_desc = "，优化搜索营销和程序化投放，提升转化效率和ROI"
        elif dominant_category[0] == "视频广告" and dominant_category[1] > 0.2:
            detail_desc = "，加强视频内容营销，提升品牌故事传播和情感连接"
        elif dominant_category[0] == "线下媒体" and dominant_category[1] > 0.2:
            detail_desc = "，强化线下场景触达，提升品牌可见度和消费者接触频次"
        else:
            detail_desc = "，实现多渠道协同效应，最大化媒体投资回报"

        return f"{digital_desc}：{focus_desc}{detail_desc}"

    def generate_optimization_schemes(self, baseline_kpi, channel_performance):
        """生成优化方案"""
        results = []

        # 添加基准方案
        if self.baseline_allocation == "equal":
            baseline_name = "基准方案（平均分配）"
            baseline_budgets = [self.total_budget / len(self.channels)] * len(self.channels)
        else:
            baseline_name = "基准方案（自定义配比）"
            baseline_budgets = []
            for channel in self.channels:
                if channel in self.custom_baseline_ratios:
                    ratio = self.custom_baseline_ratios[channel] / 100.0
                    baseline_budgets.append(self.total_budget * ratio)
                else:
                    baseline_budgets.append(self.total_budget / len(self.channels))

        baseline_budget_dict = dict(zip(self.channels, baseline_budgets))
        baseline_desc = self.generate_scheme_description(baseline_name, baseline_budget_dict)

        results.append({
            "方案名称": baseline_name,
            "方案描述": baseline_desc,
            "KPI": baseline_kpi,
            "预算分配": baseline_budget_dict,
            "提升": 0,
            "提升比例": "0.00%"
        })
        
        # 生成差异化方案序列（基于渠道类别）
        differentiated_schemes = self.generate_differentiated_schemes(baseline_kpi, channel_performance)
        results.extend(differentiated_schemes)
        
        # 方案3-N: 增强智能搜索优化
        best_random_results = []
        # 直接使用用户设置的迭代次数，不再强制最小值
        max_iterations = self.config.get("max_iterations", 200)

        print(f"📊 用户设置迭代次数: {max_iterations}")
        print(f"📊 优化方案数量: {self.optimization_schemes}")

        print(f"正在进行增强智能搜索优化（{max_iterations}次迭代）...")

        best_kpi_so_far = baseline_kpi
        no_improvement_count = 0
        high_improvement_found = False  # 是否找到10%以上提升
        target_improvement_pct = 10.0  # 目标提升10%

        for iteration in range(max_iterations):
            # 检查控制状态
            control_state = self.check_control_state()
            if control_state == "terminated":
                print(f"  优化被用户终止（{iteration}/{max_iterations}）")
                break

            # 显示进度
            if iteration % 20 == 0:
                progress = (iteration / max_iterations) * 100
                status = f"增强搜索优化 ({iteration}/{max_iterations})"
                detail = f"已找到 {len(best_random_results)} 个有效方案"

                print(f"  搜索进度: {progress:.1f}% ({iteration}/{max_iterations})")

                # 回调UI更新进度
                if self.progress_callback:
                    self.progress_callback(progress, status, detail)

            # 使用多种策略生成权重
            strategy = iteration % 5  # 循环使用5种策略

            if strategy == 0:
                # 策略1: 基于渠道效率的权重分配
                weights = self.generate_efficiency_based_weights(channel_performance)
            elif strategy == 1:
                # 策略2: 极端集中策略（80/20原则）
                weights = self.generate_concentrated_weights(channel_performance)
            elif strategy == 2:
                # 策略3: 渐进优化策略
                weights = self.generate_progressive_weights(channel_performance, iteration, max_iterations)
            elif strategy == 3:
                # 策略4: 随机探索策略
                weights = self.generate_random_exploration_weights()
            else:
                # 策略5: 混合策略
                weights = self.generate_hybrid_weights(channel_performance, iteration)

            # 归一化权重
            total_weight = sum(weights)
            if total_weight > 0:
                ratios = [w / total_weight for w in weights]
                budgets = [r * self.total_budget for r in ratios]

                kpi, final_budgets = self.write_budgets_and_calculate(budgets)
                improvement = kpi - baseline_kpi
                improvement_pct = (improvement / baseline_kpi) * 100 if baseline_kpi > 0 else 0

                # 只保存正提升的方案
                if improvement > 0:
                    # 检查是否是新的最佳结果
                    if kpi > best_kpi_so_far:
                        best_kpi_so_far = kpi
                        no_improvement_count = 0
                        print(f"    发现更好结果: KPI = {kpi:,.2f} (+{improvement_pct:.2f}%)")

                        # 检查是否达到高提升目标
                        if improvement_pct >= target_improvement_pct:
                            high_improvement_found = True
                            print(f"    🎯 达到目标提升 {target_improvement_pct}%！")
                    else:
                        no_improvement_count += 1

                    # 只保存足够好的结果（至少1%提升）
                    if improvement_pct >= 1.0:
                        scheme_name = f"增强搜索方案{len(best_random_results)+1}"
                        final_budget_dict = dict(zip(self.channels, final_budgets))
                        scheme_desc = self.generate_scheme_description(scheme_name, final_budget_dict)

                        best_random_results.append({
                            "方案名称": scheme_name,
                            "方案描述": scheme_desc,
                            "KPI": kpi,
                            "预算分配": final_budget_dict,
                            "提升": improvement,
                            "提升比例": f"{improvement_pct:.2f}%"
                        })

            # 改进的早停机制
            # 如果找到10%以上提升，可以适当提前结束
            if high_improvement_found and no_improvement_count > 50 and len(best_random_results) >= self.optimization_schemes:
                print(f"  已找到高提升方案，提前结束搜索（{iteration+1}次迭代）")
                break
            # 如果没有找到10%提升，继续搜索更长时间
            elif not high_improvement_found and no_improvement_count > 200:
                print(f"  长时间无改进，但继续搜索高提升方案（{iteration+1}次迭代）")
                no_improvement_count = 0  # 重置计数器，继续搜索

        print(f"智能搜索完成，找到 {len(best_random_results)} 个有效方案")

        # 按KPI排序，取前几个
        best_random_results.sort(key=lambda x: x["KPI"], reverse=True)
        results.extend(best_random_results[:max(1, self.optimization_schemes-len(results))])

        # 创建差异化递进排序：基准方案 + 按渠道类别差异化的优化方案
        baseline_schemes = [r for r in results if "基准方案" in r["方案名称"]]
        optimization_schemes = [r for r in results if "基准方案" not in r["方案名称"]]

        # 过滤掉负提升或零提升的方案
        positive_schemes = [r for r in optimization_schemes if float(r["提升比例"].rstrip('%')) > 0]

        # 按提升幅度从小到大排序（确保递进趋势）
        positive_schemes.sort(key=lambda x: float(x["提升比例"].rstrip('%')))

        # 如果方案数量超过配置，选择分布均匀的方案
        max_optimization_schemes = self.optimization_schemes - len(baseline_schemes)
        if len(positive_schemes) > max_optimization_schemes:
            # 选择提升幅度分布均匀的方案
            selected_schemes = self.select_evenly_distributed_schemes(positive_schemes, max_optimization_schemes)
        else:
            selected_schemes = positive_schemes

        # 重新组合：基准方案在前，然后是差异化递进的优化方案
        final_results = baseline_schemes + selected_schemes

        # 验证并调整方案名称，确保体现渠道类别差异化
        self.ensure_scheme_differentiation(final_results)

        print(f"\n📊 生成差异化递进方案序列:")
        for i, result in enumerate(final_results, 1):
            improvement_pct = result["提升比例"]
            scheme_type = self.get_scheme_category_focus(result)
            print(f"  {i}. {result['方案名称']}: {improvement_pct} 提升 ({scheme_type})")

        return final_results

    def select_evenly_distributed_schemes(self, schemes, target_count):
        """选择提升幅度分布均匀的方案"""
        if len(schemes) <= target_count:
            return schemes

        # 计算提升幅度范围
        improvements = [float(s["提升比例"].rstrip('%')) for s in schemes]
        min_improvement = min(improvements)
        max_improvement = max(improvements)

        # 计算均匀分布的目标提升幅度
        if target_count > 1:
            step = (max_improvement - min_improvement) / (target_count - 1)
            target_improvements = [min_improvement + i * step for i in range(target_count)]
        else:
            target_improvements = [max_improvement]

        # 为每个目标提升幅度选择最接近的方案
        selected = []
        used_indices = set()

        for target in target_improvements:
            best_index = -1
            best_diff = float('inf')

            for i, scheme in enumerate(schemes):
                if i in used_indices:
                    continue

                improvement = float(scheme["提升比例"].rstrip('%'))
                diff = abs(improvement - target)

                if diff < best_diff:
                    best_diff = diff
                    best_index = i

            if best_index != -1:
                selected.append(schemes[best_index])
                used_indices.add(best_index)

        return selected

    def ensure_scheme_differentiation(self, schemes):
        """确保方案差异化，避免同质化"""
        for scheme in schemes:
            if "基准方案" not in scheme["方案名称"]:
                # 分析方案的主要渠道类别
                main_category = self.get_scheme_main_category(scheme)

                # 如果方案名称没有体现类别特色，更新名称
                if not any(cat in scheme["方案名称"] for cat in ["数字", "社交", "视频", "线下", "赞助", "电商", "内容", "移动"]):
                    if main_category and main_category != "其他":
                        # 根据主要类别重新命名
                        category_names = {
                            "数字媒体": "数字营销",
                            "社交媒体": "社交互动",
                            "视频广告": "视频广告",
                            "线下媒体": "线下媒体",
                            "赞助营销": "赞助营销",
                            "电商平台": "电商平台",
                            "内容营销": "内容营销",
                            "移动应用": "移动应用"
                        }
                        category_name = category_names.get(main_category, main_category)
                        scheme["方案名称"] = f"{category_name}重点方案"

    def get_scheme_main_category(self, scheme):
        """获取方案的主要渠道类别"""
        channel_classification, _ = self.get_channel_categories()
        category_budgets = {}

        # 计算各类别的预算占比
        for category, channels_in_cat in channel_classification.items():
            total_budget_in_cat = sum(scheme['预算分配'].get(ch, 0) for ch in channels_in_cat)
            ratio = total_budget_in_cat / self.total_budget
            category_budgets[category] = ratio

        # 返回预算占比最高的类别
        if category_budgets:
            return max(category_budgets.items(), key=lambda x: x[1])[0]
        return None

    def get_scheme_category_focus(self, scheme):
        """获取方案的类别重点描述"""
        main_category = self.get_scheme_main_category(scheme)
        if main_category and main_category != "其他":
            return f"重点: {main_category}"
        else:
            return "均衡投放"

    def export_results_simple(self, results, baseline_kpi, channel_performance):
        """简化版导出结果（避免Excel COM复杂性）"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # 使用用户设置的输出目录和文件名前缀
            output_dir = self.config.get("output_directory", os.getcwd())
            output_prefix = self.config.get("output_prefix", "媒体预算优化结果")

            # 确保输出目录存在
            if not os.path.exists(output_dir):
                try:
                    os.makedirs(output_dir, exist_ok=True)
                    print(f"创建输出目录: {output_dir}")
                except Exception as e:
                    print(f"创建输出目录失败: {e}")
                    output_dir = os.getcwd()  # 回退到当前目录
                    print(f"回退到当前目录: {output_dir}")

            output_filename = os.path.join(output_dir, f"{output_prefix}_v2_{timestamp}.txt")

            print(f"正在导出结果到: {output_filename}")

            with open(output_filename, 'w', encoding='utf-8') as f:
                f.write("=" * 80 + "\n")
                f.write("媒体预算优化分析报告 v2.0\n")
                f.write("=" * 80 + "\n\n")

                # 基本信息
                f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"基准方案KPI: {baseline_kpi:,.2f}\n")
                f.write(f"总预算: ¥{self.total_budget:,}\n")

                # 约束信息
                constraint_info = self.get_constraint_summary()
                f.write(f"约束设置: {constraint_info}\n")
                f.write(f"基准类型: {'自定义配比' if self.baseline_allocation == 'custom' else '平均分配'}\n\n")

                # 优化结果
                f.write("优化结果总览:\n")
                f.write("-" * 80 + "\n")
                f.write(f"{'排名':<4} {'方案名称':<20} {'KPI':<12} {'提升':<12} {'提升比例':<10}\n")
                f.write("-" * 80 + "\n")

                for rank, result in enumerate(results, 1):
                    f.write(f"{rank:<4} {result['方案名称']:<20} {result['KPI']:<12,.0f} "
                           f"{result['提升']:<12,.0f} {result['提升比例']:<10}\n")

                # 详细预算分配
                f.write(f"\n\n详细预算分配:\n")
                f.write("-" * 80 + "\n")

                for rank, result in enumerate(results, 1):
                    f.write(f"\n{rank}. {result['方案名称']} (KPI: {result['KPI']:,.0f}, 提升: {result['提升比例']})\n")
                    f.write("-" * 60 + "\n")

                    # 按预算排序显示渠道
                    budget_items = [(ch, budget) for ch, budget in result['预算分配'].items()]
                    budget_items.sort(key=lambda x: x[1], reverse=True)

                    for channel, budget in budget_items:
                        ratio = budget / self.total_budget
                        f.write(f"  {channel:<20}: {ratio:>6.1%} (¥{budget:>10,.0f})\n")

                # 渠道效率分析
                f.write(f"\n\n渠道效率分析:\n")
                f.write("-" * 80 + "\n")
                f.write(f"{'排名':<4} {'渠道名称':<20} {'测试KPI':<12} {'vs基准提升':<12} {'效率':<10}\n")
                f.write("-" * 80 + "\n")

                for rank, perf in enumerate(channel_performance, 1):
                    f.write(f"{rank:<4} {perf['channel']:<20} {perf['kpi']:<12,.0f} "
                           f"{perf['improvement']:<12,.0f} {perf['efficiency']:<10,.2f}\n")

            print(f"结果已导出到: {output_filename}")
            return output_filename

        except Exception as e:
            print(f"导出结果时出错: {e}")
            return None

    def export_results(self, results, baseline_kpi, channel_performance):
        """导出结果到美化的Excel文件"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # 使用用户设置的输出目录和文件名前缀
            output_dir = self.config.get("output_directory", os.getcwd())
            output_prefix = self.config.get("output_prefix", "媒体预算优化结果")

            # 确保输出目录存在
            if not os.path.exists(output_dir):
                try:
                    os.makedirs(output_dir, exist_ok=True)
                    print(f"创建输出目录: {output_dir}")
                except Exception as e:
                    print(f"创建输出目录失败: {e}")
                    output_dir = os.getcwd()  # 回退到当前目录
                    print(f"回退到当前目录: {output_dir}")

            output_filename = os.path.join(output_dir, f"{output_prefix}_v2_{timestamp}.xlsx")

            print(f"正在尝试导出Excel文件: {output_filename}")

            # 使用现有的Excel实例（避免创建新实例）
            if hasattr(self, 'excel') and self.excel:
                excel_app = self.excel
            else:
                excel_app = win32com.client.Dispatch("Excel.Application")
                excel_app.Visible = False
                excel_app.DisplayAlerts = False

            # 创建新工作簿
            new_workbook = excel_app.Workbooks.Add()

            # 工作表1: 优化结果总览
            overview_sheet = new_workbook.Worksheets(1)
            overview_sheet.Name = "优化结果总览"

            # 写入基本信息 - 美化版
            title_cell = overview_sheet.Cells(1, 1)
            title_cell.Value = "📊 媒体预算优化分析报告 v3.0"
            title_cell.Font.Size = 16
            title_cell.Font.Bold = True
            title_cell.Font.Color = 0x0066CC  # 蓝色

            # 合并标题单元格
            overview_sheet.Range("A1:F1").Merge()
            overview_sheet.Range("A1:F1").HorizontalAlignment = -4108  # 居中

            info_cell2 = overview_sheet.Cells(2, 1)
            info_cell2.Value = f"🕒 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            info_cell2.Font.Size = 10

            info_cell3 = overview_sheet.Cells(3, 1)
            info_cell3.Value = f"📈 基准KPI: {baseline_kpi:,.2f}"
            info_cell3.Font.Size = 10
            info_cell3.Font.Color = 0x008000  # 绿色

            info_cell4 = overview_sheet.Cells(4, 1)
            info_cell4.Value = f"💰 总预算: ¥{self.total_budget:,}"
            info_cell4.Font.Size = 10

            kpi_type = self.config.get("kpi_type", "Non-BHT")
            info_cell5 = overview_sheet.Cells(5, 1)
            info_cell5.Value = f"🎯 KPI类型: {kpi_type}"
            info_cell5.Font.Size = 10

            constraint_info = self.get_constraint_summary()
            info_cell6 = overview_sheet.Cells(6, 1)
            info_cell6.Value = f"⚙️ 约束设置: {constraint_info}"
            info_cell6.Font.Size = 10

            # 写入结果表头 - 美化版
            headers = ["🏆 排名", "📋 方案名称", "📊 KPI", "📈 提升", "📊 提升比例", "📝 方案描述"]
            for i, header in enumerate(headers, 1):
                cell = overview_sheet.Cells(8, i)
                cell.Value = header
                cell.Font.Bold = True
                cell.Font.Color = 0xFFFFFF  # 白色字体
                cell.Interior.Color = 0x4472C4  # 蓝色背景
                cell.HorizontalAlignment = -4108  # 居中
                cell.Borders.LineStyle = 1  # 添加边框

            # 写入结果数据 - 美化版
            for rank, result in enumerate(results, 1):
                row = rank + 8

                # 排名
                rank_cell = overview_sheet.Cells(row, 1)
                rank_cell.Value = rank
                rank_cell.HorizontalAlignment = -4108  # 居中
                rank_cell.Borders.LineStyle = 1

                # 方案名称
                name_cell = overview_sheet.Cells(row, 2)
                name_cell.Value = result["方案名称"]
                name_cell.Borders.LineStyle = 1
                if rank == 1 and result["方案名称"] != "基准方案（平均分配）":
                    name_cell.Font.Bold = True
                    name_cell.Font.Color = 0x008000  # 绿色，突出最优方案

                # KPI
                kpi_cell = overview_sheet.Cells(row, 3)
                kpi_cell.Value = result["KPI"]
                kpi_cell.NumberFormat = "#,##0.00"
                kpi_cell.Borders.LineStyle = 1

                # 提升
                improvement_cell = overview_sheet.Cells(row, 4)
                improvement_cell.Value = result["提升"]
                improvement_cell.NumberFormat = "#,##0.00"
                improvement_cell.Borders.LineStyle = 1
                if result["提升"] > 0:
                    improvement_cell.Font.Color = 0x008000  # 绿色表示正提升

                # 提升比例
                pct_cell = overview_sheet.Cells(row, 5)
                pct_cell.Value = result["提升比例"]
                pct_cell.Borders.LineStyle = 1
                if result["提升"] > 0:
                    pct_cell.Font.Color = 0x008000  # 绿色表示正提升

                # 方案描述
                desc_cell = overview_sheet.Cells(row, 6)
                desc_cell.Value = result["方案描述"]
                desc_cell.Borders.LineStyle = 1

            # 工作表2: 详细预算分配
            detail_sheet = new_workbook.Worksheets.Add()
            detail_sheet.Name = "详细预算分配"

            # 表头
            detail_headers = ["方案名称", "KPI", "提升比例"] + self.channels
            for i, header in enumerate(detail_headers, 1):
                cell = detail_sheet.Cells(1, i)
                cell.Value = header
                cell.Font.Bold = True

            # 数据
            for rank, result in enumerate(results, 1):
                row = rank + 1
                detail_sheet.Cells(row, 1).Value = result["方案名称"]
                detail_sheet.Cells(row, 2).Value = result["KPI"]
                detail_sheet.Cells(row, 3).Value = result["提升比例"]

                for i, channel in enumerate(self.channels):
                    budget = result["预算分配"][channel]
                    ratio = budget / self.total_budget
                    detail_sheet.Cells(row, 4 + i).Value = ratio
                    detail_sheet.Cells(row, 4 + i).NumberFormat = "0.00%"

            # 工作表3: 原始输入数据
            raw_data_sheet = new_workbook.Worksheets.Add()
            raw_data_sheet.Name = "原始输入数据"

            raw_data_sheet.Cells(1, 1).Value = "原始输入数据（可直接复制到Excel验证）"
            raw_data_sheet.Cells(1, 1).Font.Bold = True
            raw_data_sheet.Cells(2, 1).Value = f"输入区域: {chr(64+self.input_start_col)}{self.input_start_row}:{chr(64+self.input_end_col)}{self.input_end_row}"
            raw_data_sheet.Cells(3, 1).Value = f"输出区域: {chr(64+self.output_col)}{self.output_start_row}:{chr(64+self.output_col)}{self.output_end_row}"

            # 表头
            raw_data_sheet.Cells(5, 1).Value = "方案名称"
            for i, channel in enumerate(self.channels, 2):
                raw_data_sheet.Cells(5, i).Value = channel

            # 每个方案的原始数据
            for rank, result in enumerate(results, 1):
                row = rank + 5
                raw_data_sheet.Cells(row, 1).Value = result["方案名称"]

                # 计算每行的预算分配
                budgets = [result["预算分配"][ch] for ch in self.channels]
                num_rows = self.input_end_row - self.input_start_row + 1
                budgets_per_row = [budget / num_rows for budget in budgets]

                for i, budget_per_row in enumerate(budgets_per_row, 2):
                    raw_data_sheet.Cells(row, i).Value = budget_per_row
                    raw_data_sheet.Cells(row, i).NumberFormat = "0.00"

            # 工作表4: 业务对比分析
            analysis_sheet = new_workbook.Worksheets.Add()
            analysis_sheet.Name = "业务对比分析"

            analysis_sheet.Cells(1, 1).Value = "广告业务对比分析"
            analysis_sheet.Cells(1, 1).Font.Size = 14
            analysis_sheet.Cells(1, 1).Font.Bold = True

            # 渠道分类分析
            analysis_sheet.Cells(3, 1).Value = "渠道类型分析"
            analysis_sheet.Cells(3, 1).Font.Bold = True

            # 使用通用渠道分类系统
            channel_categories, _ = self.get_channel_categories()

            # 分析表头
            analysis_headers = ["方案名称"] + list(channel_categories.keys()) + ["数字化比例", "传统媒体比例"]
            for i, header in enumerate(analysis_headers, 1):
                cell = analysis_sheet.Cells(5, i)
                cell.Value = header
                cell.Font.Bold = True

            # 分析每个方案的渠道分布
            for rank, result in enumerate(results, 1):
                row = rank + 5
                analysis_sheet.Cells(row, 1).Value = result["方案名称"]

                # 计算各类型渠道的预算占比
                category_budgets = {}
                for category, channels_in_cat in channel_categories.items():
                    total_budget_in_cat = sum(result["预算分配"].get(ch, 0) for ch in channels_in_cat if ch in result["预算分配"])
                    ratio = total_budget_in_cat / self.total_budget
                    category_budgets[category] = ratio
                    analysis_sheet.Cells(row, list(channel_categories.keys()).index(category) + 2).Value = ratio
                    analysis_sheet.Cells(row, list(channel_categories.keys()).index(category) + 2).NumberFormat = "0.00%"

                # 计算数字化比例（数字媒体+社交媒体）
                digital_ratio = category_budgets.get("数字媒体", 0) + category_budgets.get("社交媒体", 0)
                analysis_sheet.Cells(row, len(channel_categories) + 2).Value = digital_ratio
                analysis_sheet.Cells(row, len(channel_categories) + 2).NumberFormat = "0.00%"

                # 计算传统媒体比例（线下媒体+视频广告）
                traditional_ratio = category_budgets.get("线下媒体", 0) + category_budgets.get("视频广告", 0)
                analysis_sheet.Cells(row, len(channel_categories) + 3).Value = traditional_ratio
                analysis_sheet.Cells(row, len(channel_categories) + 3).NumberFormat = "0.00%"

            # 添加业务洞察
            insight_start_row = len(results) + 8
            analysis_sheet.Cells(insight_start_row, 1).Value = "业务洞察与建议"
            analysis_sheet.Cells(insight_start_row, 1).Font.Bold = True

            # 找出最优方案
            best_result = max(results, key=lambda x: x["KPI"])

            insights = [
                f"• 最优方案: {best_result['方案名称']}，KPI提升 {best_result['提升比例']}",
                f"• 基准KPI: {baseline_kpi:,.0f}，最优KPI: {best_result['KPI']:,.0f}",
                "",
                "渠道效率分析:",
            ]

            # 添加渠道效率洞察
            top_channels = sorted(channel_performance, key=lambda x: x['improvement'], reverse=True)[:3]
            for i, perf in enumerate(top_channels, 1):
                insights.append(f"  {i}. {perf['channel']}: 效率最高，建议增加投入")

            # 添加预算分配建议
            insights.extend([
                "",
                "预算分配建议:",
                f"• 数字媒体占比建议: 40-60%（精准触达）",
                f"• 社交媒体占比建议: 20-35%（品牌互动）",
                f"• 传统媒体占比建议: 15-25%（品牌曝光）",
                f"• 赞助营销占比建议: 5-15%（品牌联想）"
            ])

            # 写入洞察内容
            for i, insight in enumerate(insights):
                analysis_sheet.Cells(insight_start_row + 1 + i, 1).Value = insight

            # 工作表5: 渠道效率分析
            efficiency_sheet = new_workbook.Worksheets.Add()
            efficiency_sheet.Name = "渠道效率分析"

            efficiency_sheet.Cells(1, 1).Value = "渠道效率分析"
            efficiency_sheet.Cells(1, 1).Font.Size = 14
            efficiency_sheet.Cells(1, 1).Font.Bold = True

            # 表头
            eff_headers = ["排名", "渠道名称", "单独测试KPI", "vs基准提升", "效率指数", "建议动作"]
            for i, header in enumerate(eff_headers, 1):
                cell = efficiency_sheet.Cells(3, i)
                cell.Value = header
                cell.Font.Bold = True

            # 渠道效率数据
            for rank, perf in enumerate(channel_performance, 1):
                row = rank + 3
                efficiency_sheet.Cells(row, 1).Value = rank
                efficiency_sheet.Cells(row, 2).Value = perf['channel']
                efficiency_sheet.Cells(row, 3).Value = perf['kpi']
                efficiency_sheet.Cells(row, 4).Value = perf['improvement']
                efficiency_sheet.Cells(row, 5).Value = perf['efficiency']

                # 根据效率给出建议
                if perf['improvement'] > 500:
                    suggestion = "强烈建议增加投入"
                elif perf['improvement'] > 100:
                    suggestion = "建议适度增加投入"
                elif perf['improvement'] > 0:
                    suggestion = "维持当前投入"
                elif perf['improvement'] > -100:
                    suggestion = "考虑减少投入"
                else:
                    suggestion = "建议大幅减少投入"

                efficiency_sheet.Cells(row, 6).Value = suggestion

            # 自动调整列宽
            try:
                for sheet in [overview_sheet, detail_sheet, raw_data_sheet, analysis_sheet, efficiency_sheet]:
                    sheet.Columns.AutoFit()
            except:
                pass

            # 保存文件
            output_path = os.path.abspath(output_filename)
            new_workbook.SaveAs(output_path)
            new_workbook.Close(SaveChanges=False)

            print(f"Excel文件已成功保存: {output_filename}")
            print(f"包含工作表: 优化结果总览, 详细预算分配, 原始输入数据, 业务对比分析, 渠道效率分析")
            return output_filename

        except Exception as excel_error:
            print(f"Excel导出失败: {excel_error}")
            # 如果Excel导出失败，尝试生成简化版文本报告作为备份
            try:
                text_file = self.export_results_simple(results, baseline_kpi, channel_performance)
                print(f"已生成备份文本报告: {text_file}")
                return text_file
            except Exception as text_error:
                print(f"文本导出也失败: {text_error}")
                raise Exception(f"所有导出方式都失败了: Excel错误={excel_error}, 文本错误={text_error}")

    def optimize(self):
        """执行优化"""
        if not self.initialize_excel():
            raise Exception("无法初始化Excel连接")

        try:
            print(f"开始优化分析...")
            print(f"基准类型: {'自定义配比' if self.baseline_allocation == 'custom' else '平均分配'}")
            print(f"约束范围: {self.default_min_ratio:.1%} - {self.default_max_ratio:.1%}")

            # 进度回调：初始化
            if self.progress_callback:
                self.progress_callback(5, "初始化Excel连接", "正在连接Excel...")

            # 获取基准KPI
            baseline_kpi, _ = self.get_baseline_kpi()
            print(f"基准方案KPI: {baseline_kpi:,.2f}")

            # 进度回调：基准计算完成
            if self.progress_callback:
                self.progress_callback(15, "基准方案计算完成", f"基准KPI: {baseline_kpi:,.2f}")

            # 测试各渠道表现
            print("正在测试各渠道单独表现...")
            if self.progress_callback:
                self.progress_callback(20, "测试各渠道表现", f"测试 {len(self.channels)} 个渠道...")

            channel_performance = self.test_individual_channels(baseline_kpi)

            # 显示渠道效率排名
            print(f"\n渠道效率排名（前5名）:")
            for i, perf in enumerate(channel_performance[:5], 1):
                print(f"{i}. {perf['channel']}: 提升 {perf['improvement']:+,.0f}")

            # 进度回调：渠道测试完成
            if self.progress_callback:
                self.progress_callback(40, "渠道测试完成", f"找到 {len([p for p in channel_performance if p['improvement'] > 0])} 个高效渠道")

            # 生成优化方案
            print(f"\n正在生成 {self.optimization_schemes} 个优化方案...")
            if self.progress_callback:
                self.progress_callback(45, "生成优化方案", "正在生成基础方案...")

            results = self.generate_optimization_schemes(baseline_kpi, channel_performance)

            # 显示最优方案
            if len(results) > 1:
                best_result = results[0] if results[0]["方案名称"] != "基准方案" else results[1]
                print(f"\n最优方案: {best_result['方案名称']}")
                print(f"KPI: {best_result['KPI']:,.2f}")
                print(f"提升: {best_result['提升']:+,.2f} ({best_result['提升比例']})")

                # 进度回调：优化完成
                if self.progress_callback:
                    self.progress_callback(95, "优化完成", f"最优提升: {best_result['提升比例']}")

            # 导出结果
            print("\n正在导出结果...")
            if self.progress_callback:
                self.progress_callback(98, "导出结果", "正在生成Excel报告...")

            output_file = self.export_results(results, baseline_kpi, channel_performance)

            # 进度回调：全部完成
            if self.progress_callback:
                self.progress_callback(100, "全部完成", f"已生成 {len(results)} 个方案")

            return {
                "results": results,
                "baseline_kpi": baseline_kpi,
                "channel_performance": channel_performance,
                "output_file": output_file
            }

        finally:
            self.cleanup_excel()
