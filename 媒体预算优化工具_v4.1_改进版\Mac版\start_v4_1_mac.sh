#!/bin/bash

echo ""
echo "========================================"
echo "   Media Budget Optimizer v4.1"
echo "   Mac Version with Enhanced Layout"
echo "========================================"
echo ""
echo "New Features in v4.1:"
echo "- Output data range configuration"
echo "- Unified scrollable interface"
echo "- Enhanced window responsiveness"
echo "- Cross-platform Mac compatibility"
echo ""

# Check Python environment
if ! command -v python3 &> /dev/null; then
    echo "ERROR: Python 3 not found!"
    echo ""
    echo "Solution:"
    echo "1. Install Python 3.7+ from python.org"
    echo "2. Or install via Homebrew: brew install python"
    echo ""
    read -p "Press Enter to exit..."
    exit 1
fi

echo "Python environment OK"
echo ""

# Check dependencies
echo "Checking dependencies..."

# Check openpyxl
python3 -c "import openpyxl" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "Installing openpyxl..."
    pip3 install openpyxl
    if [ $? -ne 0 ]; then
        echo "Installation failed!"
        echo "Please run manually: pip3 install openpyxl"
        read -p "Press Enter to exit..."
        exit 1
    fi
    echo "openpyxl installed successfully"
fi

# Check tkinter
python3 -c "import tkinter" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "ERROR: tkinter not found!"
    echo "Please install tkinter:"
    echo "- On macOS: Usually included with Python"
    echo "- If missing: brew install python-tk"
    read -p "Press Enter to exit..."
    exit 1
fi

echo "Dependencies OK"
echo ""

# Start v4.1 Mac version
echo "Starting Media Budget Optimizer v4.1 Mac Version..."
echo ""

python3 media_optimizer_v4_1_mac.py

# Cleanup after exit
echo ""
echo "Program exited"
echo "Thank you for using Media Budget Optimizer v4.1 Mac Version!"

sleep 2
