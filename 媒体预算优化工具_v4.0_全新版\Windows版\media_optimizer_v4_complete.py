#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
媒体预算优化工具 v4.0 - 完整功能版
解决所有问题：自适应布局、Excel读取、渠道分类、完整功能测试
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import win32com.client
import threading
from datetime import datetime
import re
import sys
import traceback
import os
import random
import time

class MediaOptimizerV4Complete:
    def __init__(self):
        try:
            print("初始化媒体预算优化工具 v4.0 完整版...")
            self.root = tk.Tk()
            print("✅ 创建主窗口成功")
            
            # 初始化数据
            self.channels_data = []
            self.channel_types = {}
            self.channel_constraints = {}
            self.optimization_running = False
            self.excel_app = None
            self.workbook = None
            self.worksheet = None
            
            self.setup_window()
            self.setup_styles()
            self.setup_variables()
            self.create_responsive_layout()
            
            print("✅ 程序初始化完成！")
            
        except Exception as e:
            print(f"❌ 初始化失败: {e}")
            traceback.print_exc()
            raise
        
    def setup_window(self):
        """设置主窗口"""
        try:
            self.root.title("媒体预算优化工具 v4.0 - 完整功能版")
            self.root.geometry("1400x900")
            self.root.minsize(1200, 700)
            
            # 设置窗口样式
            self.root.configure(bg='#f5f5f5')
            
            # 窗口居中
            self.center_window()
            
            # 设置关闭事件
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            
        except Exception as e:
            print(f"❌ 窗口设置失败: {e}")
            raise
        
    def center_window(self):
        """窗口居中显示"""
        try:
            self.root.update_idletasks()
            width = self.root.winfo_width()
            height = self.root.winfo_height()
            x = (self.root.winfo_screenwidth() // 2) - (width // 2)
            y = (self.root.winfo_screenheight() // 2) - (height // 2)
            self.root.geometry(f'{width}x{height}+{x}+{y}')
        except Exception as e:
            print(f"⚠️ 窗口居中失败: {e}")
        
    def setup_styles(self):
        """设置样式"""
        try:
            self.style = ttk.Style()
            self.style.theme_use('clam')
            
            # 定义颜色主题
            self.colors = {
                'primary': '#2E86AB',
                'secondary': '#A23B72', 
                'accent': '#F18F01',
                'success': '#28a745',
                'background': '#f5f5f5',
                'surface': '#ffffff',
                'text': '#2C3E50',
                'text_light': '#6c757d',
                'border': '#dee2e6'
            }
            
            # 配置样式
            self.style.configure('Card.TFrame',
                               background=self.colors['surface'],
                               relief='solid',
                               borderwidth=1)
            
            self.style.configure('Title.TLabel',
                               background=self.colors['surface'],
                               foreground=self.colors['primary'],
                               font=('Microsoft YaHei UI', 12, 'bold'))
            
            self.style.configure('Heading.TLabel',
                               background=self.colors['surface'],
                               foreground=self.colors['text'],
                               font=('Microsoft YaHei UI', 10, 'bold'))
            
            self.style.configure('Normal.TLabel',
                               background=self.colors['surface'],
                               foreground=self.colors['text'],
                               font=('Microsoft YaHei UI', 9))
            
        except Exception as e:
            print(f"❌ 样式设置失败: {e}")
            self.colors = {'primary': '#0078d4', 'surface': '#ffffff', 'text': '#000000'}
        
    def setup_variables(self):
        """设置变量"""
        try:
            # 文件配置
            self.file_path_var = tk.StringVar()
            self.sheet_name_var = tk.StringVar()
            
            # 数据范围
            self.data_start_row_var = tk.StringVar()
            self.data_end_row_var = tk.StringVar()
            self.data_start_col_var = tk.StringVar()
            self.data_end_col_var = tk.StringVar()
            self.channel_row_var = tk.StringVar()
            
            # 基准比例设置
            self.baseline_method_var = tk.StringVar(value="manual")
            self.baseline_start_row_var = tk.StringVar()
            self.baseline_end_row_var = tk.StringVar()
            self.baseline_start_col_var = tk.StringVar()
            self.baseline_end_col_var = tk.StringVar()
            
            # 优化参数
            self.total_budget_var = tk.StringVar()
            self.num_solutions_var = tk.StringVar(value="5")
            self.iterations_var = tk.StringVar(value="100")
            self.kpi_type_var = tk.StringVar(value="Non-BHT")
            
            # 实时监控
            self.baseline_kpi_var = tk.StringVar(value="0")
            self.current_best_kpi_var = tk.StringVar(value="0")
            self.improvement_var = tk.StringVar(value="0%")
            self.progress_var = tk.StringVar(value="0%")
            self.status_var = tk.StringVar(value="就绪")
            
        except Exception as e:
            print(f"❌ 变量设置失败: {e}")
            raise
            
    def create_responsive_layout(self):
        """创建响应式布局"""
        try:
            # 主容器 - 使用grid布局实现真正的响应式
            self.main_frame = tk.Frame(self.root, bg=self.colors['background'])
            self.main_frame.pack(fill='both', expand=True, padx=10, pady=10)
            
            # 配置grid权重，实现响应式布局
            self.main_frame.grid_columnconfigure(0, weight=1)  # 左侧配置区
            self.main_frame.grid_columnconfigure(1, weight=1)  # 右侧监控区
            self.main_frame.grid_rowconfigure(0, weight=1)     # 主要内容区
            
            # 创建左右两个主要区域
            self.create_left_config_area()
            self.create_right_monitor_area()
            
            # 绑定窗口大小变化事件
            self.root.bind('<Configure>', self.on_window_resize)
            
        except Exception as e:
            print(f"❌ 布局创建失败: {e}")
            self.create_simple_layout()
            
    def create_left_config_area(self):
        """创建左侧配置区域"""
        try:
            # 左侧滚动区域
            self.left_frame = tk.Frame(self.main_frame, bg=self.colors['background'])
            self.left_frame.grid(row=0, column=0, sticky='nsew', padx=(0, 5))
            
            # 创建Canvas和Scrollbar实现滚动
            self.left_canvas = tk.Canvas(self.left_frame, bg=self.colors['background'], highlightthickness=0)
            self.left_scrollbar = ttk.Scrollbar(self.left_frame, orient="vertical", command=self.left_canvas.yview)
            self.left_scrollable_frame = tk.Frame(self.left_canvas, bg=self.colors['background'])
            
            # 配置滚动
            self.left_scrollable_frame.bind(
                "<Configure>",
                lambda e: self.left_canvas.configure(scrollregion=self.left_canvas.bbox("all"))
            )
            
            self.left_canvas.create_window((0, 0), window=self.left_scrollable_frame, anchor="nw")
            self.left_canvas.configure(yscrollcommand=self.left_scrollbar.set)
            
            # 布局Canvas和Scrollbar
            self.left_canvas.pack(side="left", fill="both", expand=True)
            self.left_scrollbar.pack(side="right", fill="y")
            
            # 创建配置卡片
            self.create_file_config_section()
            self.create_data_range_section()
            self.create_baseline_config_section()
            self.create_optimization_config_section()
            self.create_channel_management_section()
            
            # 绑定鼠标滚轮
            self.bind_mousewheel(self.left_canvas)
            
        except Exception as e:
            print(f"❌ 左侧区域创建失败: {e}")
            
    def create_right_monitor_area(self):
        """创建右侧监控区域"""
        try:
            # 右侧监控区域
            self.right_frame = tk.Frame(self.main_frame, bg=self.colors['background'])
            self.right_frame.grid(row=0, column=1, sticky='nsew', padx=(5, 0))
            
            # 配置右侧区域的行权重
            self.right_frame.grid_rowconfigure(0, weight=0)  # 监控指标
            self.right_frame.grid_rowconfigure(1, weight=1)  # 结果预览
            self.right_frame.grid_rowconfigure(2, weight=0)  # 日志
            self.right_frame.grid_columnconfigure(0, weight=1)
            
            # 创建监控组件
            self.create_monitoring_section()
            self.create_results_section()
            self.create_log_section()
            
        except Exception as e:
            print(f"❌ 右侧区域创建失败: {e}")
            
    def create_file_config_section(self):
        """创建文件配置区域"""
        try:
            # 文件配置卡片
            card = tk.Frame(self.left_scrollable_frame, bg=self.colors['surface'], relief='solid', bd=1)
            card.pack(fill='x', pady=(0, 10))
            
            # 标题
            title_frame = tk.Frame(card, bg=self.colors['surface'])
            title_frame.pack(fill='x', padx=15, pady=(15, 10))
            
            tk.Label(title_frame, text="📁 文件配置", 
                    bg=self.colors['surface'], fg=self.colors['primary'],
                    font=('Microsoft YaHei UI', 12, 'bold')).pack(anchor='w')
            
            # 文件选择
            file_frame = tk.Frame(card, bg=self.colors['surface'])
            file_frame.pack(fill='x', padx=15, pady=5)
            
            tk.Label(file_frame, text="Excel文件:", 
                    bg=self.colors['surface'], fg=self.colors['text'],
                    font=('Microsoft YaHei UI', 9)).pack(anchor='w')
            
            file_input_frame = tk.Frame(file_frame, bg=self.colors['surface'])
            file_input_frame.pack(fill='x', pady=(5, 0))
            
            self.file_entry = tk.Entry(file_input_frame, textvariable=self.file_path_var, 
                                     state='readonly', font=('Microsoft YaHei UI', 9))
            self.file_entry.pack(side='left', fill='x', expand=True, padx=(0, 10))
            
            tk.Button(file_input_frame, text="浏览", command=self.browse_file,
                     font=('Microsoft YaHei UI', 9), bg=self.colors['primary'], 
                     fg='white', relief='flat').pack(side='right')
            
            # 工作表选择
            sheet_frame = tk.Frame(card, bg=self.colors['surface'])
            sheet_frame.pack(fill='x', padx=15, pady=5)
            
            tk.Label(sheet_frame, text="工作表:", 
                    bg=self.colors['surface'], fg=self.colors['text'],
                    font=('Microsoft YaHei UI', 9)).pack(anchor='w')
            
            self.sheet_combo = ttk.Combobox(sheet_frame, textvariable=self.sheet_name_var, 
                                          state='readonly', font=('Microsoft YaHei UI', 9))
            self.sheet_combo.pack(fill='x', pady=(5, 15))
            
        except Exception as e:
            print(f"❌ 文件配置区域创建失败: {e}")
            
    def create_data_range_section(self):
        """创建数据范围配置区域"""
        try:
            # 数据范围卡片
            card = tk.Frame(self.left_scrollable_frame, bg=self.colors['surface'], relief='solid', bd=1)
            card.pack(fill='x', pady=(0, 10))
            
            # 标题
            title_frame = tk.Frame(card, bg=self.colors['surface'])
            title_frame.pack(fill='x', padx=15, pady=(15, 10))
            
            tk.Label(title_frame, text="📊 数据范围配置", 
                    bg=self.colors['surface'], fg=self.colors['primary'],
                    font=('Microsoft YaHei UI', 12, 'bold')).pack(anchor='w')
            
            # 数据行范围
            row_frame = tk.Frame(card, bg=self.colors['surface'])
            row_frame.pack(fill='x', padx=15, pady=5)
            
            tk.Label(row_frame, text="数据行范围:", 
                    bg=self.colors['surface'], fg=self.colors['text'],
                    font=('Microsoft YaHei UI', 9)).pack(anchor='w')
            
            row_input_frame = tk.Frame(row_frame, bg=self.colors['surface'])
            row_input_frame.pack(fill='x', pady=(5, 0))
            
            tk.Entry(row_input_frame, textvariable=self.data_start_row_var, 
                    font=('Microsoft YaHei UI', 9), width=8).pack(side='left', padx=(0, 5))
            tk.Label(row_input_frame, text="到", bg=self.colors['surface'], 
                    font=('Microsoft YaHei UI', 9)).pack(side='left', padx=5)
            tk.Entry(row_input_frame, textvariable=self.data_end_row_var, 
                    font=('Microsoft YaHei UI', 9), width=8).pack(side='left', padx=(5, 0))
            
            # 数据列范围
            col_frame = tk.Frame(card, bg=self.colors['surface'])
            col_frame.pack(fill='x', padx=15, pady=5)
            
            tk.Label(col_frame, text="数据列范围:", 
                    bg=self.colors['surface'], fg=self.colors['text'],
                    font=('Microsoft YaHei UI', 9)).pack(anchor='w')
            
            col_input_frame = tk.Frame(col_frame, bg=self.colors['surface'])
            col_input_frame.pack(fill='x', pady=(5, 0))
            
            tk.Entry(col_input_frame, textvariable=self.data_start_col_var, 
                    font=('Microsoft YaHei UI', 9), width=8).pack(side='left', padx=(0, 5))
            tk.Label(col_input_frame, text="到", bg=self.colors['surface'], 
                    font=('Microsoft YaHei UI', 9)).pack(side='left', padx=5)
            tk.Entry(col_input_frame, textvariable=self.data_end_col_var, 
                    font=('Microsoft YaHei UI', 9), width=8).pack(side='left', padx=(5, 0))
            
            # 渠道名称行
            channel_frame = tk.Frame(card, bg=self.colors['surface'])
            channel_frame.pack(fill='x', padx=15, pady=(5, 15))
            
            tk.Label(channel_frame, text="渠道名称行:", 
                    bg=self.colors['surface'], fg=self.colors['text'],
                    font=('Microsoft YaHei UI', 9)).pack(anchor='w')
            tk.Entry(channel_frame, textvariable=self.channel_row_var, 
                    font=('Microsoft YaHei UI', 9), width=8).pack(anchor='w', pady=(5, 0))
            
        except Exception as e:
            print(f"❌ 数据范围区域创建失败: {e}")

    def create_baseline_config_section(self):
        """创建基准比例配置区域"""
        try:
            # 基准比例卡片
            card = tk.Frame(self.left_scrollable_frame, bg=self.colors['surface'], relief='solid', bd=1)
            card.pack(fill='x', pady=(0, 10))

            # 标题
            title_frame = tk.Frame(card, bg=self.colors['surface'])
            title_frame.pack(fill='x', padx=15, pady=(15, 10))

            tk.Label(title_frame, text="⚖️ 基准比例设置",
                    bg=self.colors['surface'], fg=self.colors['primary'],
                    font=('Microsoft YaHei UI', 12, 'bold')).pack(anchor='w')

            # 基准比例方法选择
            method_frame = tk.Frame(card, bg=self.colors['surface'])
            method_frame.pack(fill='x', padx=15, pady=5)

            tk.Label(method_frame, text="计算方式:",
                    bg=self.colors['surface'], fg=self.colors['text'],
                    font=('Microsoft YaHei UI', 9)).pack(anchor='w')

            method_radio_frame = tk.Frame(method_frame, bg=self.colors['surface'])
            method_radio_frame.pack(fill='x', pady=(5, 0))

            tk.Radiobutton(method_radio_frame, text="手动设置", variable=self.baseline_method_var,
                          value="manual", command=self.on_baseline_method_change,
                          bg=self.colors['surface'], font=('Microsoft YaHei UI', 9)).pack(anchor='w')
            tk.Radiobutton(method_radio_frame, text="数据表计算", variable=self.baseline_method_var,
                          value="data_table", command=self.on_baseline_method_change,
                          bg=self.colors['surface'], font=('Microsoft YaHei UI', 9)).pack(anchor='w')
            tk.Radiobutton(method_radio_frame, text="平均分配", variable=self.baseline_method_var,
                          value="average", command=self.on_baseline_method_change,
                          bg=self.colors['surface'], font=('Microsoft YaHei UI', 9)).pack(anchor='w')

            # 数据表计算范围（默认隐藏）
            self.baseline_range_frame = tk.Frame(card, bg=self.colors['surface'])

            tk.Label(self.baseline_range_frame, text="基准数据范围:",
                    bg=self.colors['surface'], fg=self.colors['text'],
                    font=('Microsoft YaHei UI', 10, 'bold')).pack(anchor='w', padx=15, pady=(10, 5))

            # 基准行范围
            baseline_row_frame = tk.Frame(self.baseline_range_frame, bg=self.colors['surface'])
            baseline_row_frame.pack(fill='x', padx=15, pady=5)

            tk.Label(baseline_row_frame, text="行范围:",
                    bg=self.colors['surface'], fg=self.colors['text'],
                    font=('Microsoft YaHei UI', 9)).pack(anchor='w')

            baseline_row_input_frame = tk.Frame(baseline_row_frame, bg=self.colors['surface'])
            baseline_row_input_frame.pack(fill='x', pady=(5, 0))

            tk.Entry(baseline_row_input_frame, textvariable=self.baseline_start_row_var,
                    font=('Microsoft YaHei UI', 9), width=8).pack(side='left', padx=(0, 5))
            tk.Label(baseline_row_input_frame, text="到", bg=self.colors['surface'],
                    font=('Microsoft YaHei UI', 9)).pack(side='left', padx=5)
            tk.Entry(baseline_row_input_frame, textvariable=self.baseline_end_row_var,
                    font=('Microsoft YaHei UI', 9), width=8).pack(side='left', padx=(5, 0))

            # 基准列范围
            baseline_col_frame = tk.Frame(self.baseline_range_frame, bg=self.colors['surface'])
            baseline_col_frame.pack(fill='x', padx=15, pady=(5, 15))

            tk.Label(baseline_col_frame, text="列范围:",
                    bg=self.colors['surface'], fg=self.colors['text'],
                    font=('Microsoft YaHei UI', 9)).pack(anchor='w')

            baseline_col_input_frame = tk.Frame(baseline_col_frame, bg=self.colors['surface'])
            baseline_col_input_frame.pack(fill='x', pady=(5, 0))

            tk.Entry(baseline_col_input_frame, textvariable=self.baseline_start_col_var,
                    font=('Microsoft YaHei UI', 9), width=8).pack(side='left', padx=(0, 5))
            tk.Label(baseline_col_input_frame, text="到", bg=self.colors['surface'],
                    font=('Microsoft YaHei UI', 9)).pack(side='left', padx=5)
            tk.Entry(baseline_col_input_frame, textvariable=self.baseline_end_col_var,
                    font=('Microsoft YaHei UI', 9), width=8).pack(side='left', padx=(5, 0))

        except Exception as e:
            print(f"❌ 基准比例区域创建失败: {e}")

    def create_optimization_config_section(self):
        """创建优化配置区域"""
        try:
            # 优化配置卡片
            card = tk.Frame(self.left_scrollable_frame, bg=self.colors['surface'], relief='solid', bd=1)
            card.pack(fill='x', pady=(0, 10))

            # 标题
            title_frame = tk.Frame(card, bg=self.colors['surface'])
            title_frame.pack(fill='x', padx=15, pady=(15, 10))

            tk.Label(title_frame, text="⚙️ 优化配置",
                    bg=self.colors['surface'], fg=self.colors['primary'],
                    font=('Microsoft YaHei UI', 12, 'bold')).pack(anchor='w')

            # 总预算
            budget_frame = tk.Frame(card, bg=self.colors['surface'])
            budget_frame.pack(fill='x', padx=15, pady=5)

            tk.Label(budget_frame, text="总预算:",
                    bg=self.colors['surface'], fg=self.colors['text'],
                    font=('Microsoft YaHei UI', 9)).pack(anchor='w')
            tk.Entry(budget_frame, textvariable=self.total_budget_var,
                    font=('Microsoft YaHei UI', 9)).pack(fill='x', pady=(5, 0))

            # KPI类型
            kpi_frame = tk.Frame(card, bg=self.colors['surface'])
            kpi_frame.pack(fill='x', padx=15, pady=5)

            tk.Label(kpi_frame, text="KPI类型:",
                    bg=self.colors['surface'], fg=self.colors['text'],
                    font=('Microsoft YaHei UI', 9)).pack(anchor='w')

            kpi_radio_frame = tk.Frame(kpi_frame, bg=self.colors['surface'])
            kpi_radio_frame.pack(fill='x', pady=(5, 0))

            tk.Radiobutton(kpi_radio_frame, text="Non-BHT (求和)",
                          variable=self.kpi_type_var, value="Non-BHT",
                          bg=self.colors['surface'], font=('Microsoft YaHei UI', 9)).pack(anchor='w')
            tk.Radiobutton(kpi_radio_frame, text="BHT (平均)",
                          variable=self.kpi_type_var, value="BHT",
                          bg=self.colors['surface'], font=('Microsoft YaHei UI', 9)).pack(anchor='w')

            # 方案数量和迭代次数
            params_frame = tk.Frame(card, bg=self.colors['surface'])
            params_frame.pack(fill='x', padx=15, pady=5)

            # 方案数量
            solutions_frame = tk.Frame(params_frame, bg=self.colors['surface'])
            solutions_frame.pack(fill='x', pady=5)

            tk.Label(solutions_frame, text="生成方案数:",
                    bg=self.colors['surface'], fg=self.colors['text'],
                    font=('Microsoft YaHei UI', 9)).pack(anchor='w')
            tk.Entry(solutions_frame, textvariable=self.num_solutions_var,
                    font=('Microsoft YaHei UI', 9), width=10).pack(anchor='w', pady=(5, 0))

            # 迭代次数
            iterations_frame = tk.Frame(params_frame, bg=self.colors['surface'])
            iterations_frame.pack(fill='x', pady=(5, 15))

            tk.Label(iterations_frame, text="迭代次数:",
                    bg=self.colors['surface'], fg=self.colors['text'],
                    font=('Microsoft YaHei UI', 9)).pack(anchor='w')
            tk.Entry(iterations_frame, textvariable=self.iterations_var,
                    font=('Microsoft YaHei UI', 9), width=10).pack(anchor='w', pady=(5, 0))

        except Exception as e:
            print(f"❌ 优化配置区域创建失败: {e}")

    def on_baseline_method_change(self):
        """基准比例方法改变时的处理"""
        try:
            method = self.baseline_method_var.get()
            if method == "data_table":
                self.baseline_range_frame.pack(fill='x', pady=(0, 15))
            else:
                self.baseline_range_frame.pack_forget()
        except Exception as e:
            print(f"基准方法切换失败: {e}")

    def create_channel_management_section(self):
        """创建渠道管理区域"""
        try:
            # 渠道管理卡片
            card = tk.Frame(self.left_scrollable_frame, bg=self.colors['surface'], relief='solid', bd=1)
            card.pack(fill='x', pady=(0, 10))

            # 标题
            title_frame = tk.Frame(card, bg=self.colors['surface'])
            title_frame.pack(fill='x', padx=15, pady=(15, 10))

            tk.Label(title_frame, text="🎯 渠道管理",
                    bg=self.colors['surface'], fg=self.colors['primary'],
                    font=('Microsoft YaHei UI', 12, 'bold')).pack(anchor='w')

            # 读取渠道按钮
            read_frame = tk.Frame(card, bg=self.colors['surface'])
            read_frame.pack(fill='x', padx=15, pady=(0, 10))

            tk.Button(read_frame, text="📊 读取渠道信息", command=self.read_channels,
                     font=('Microsoft YaHei UI', 9), bg=self.colors['secondary'],
                     fg='white', relief='flat').pack(anchor='w')

            # 渠道列表容器
            self.channels_container = tk.Frame(card, bg=self.colors['surface'])
            self.channels_container.pack(fill='x', padx=15, pady=(0, 10))

            # 开始优化按钮
            start_frame = tk.Frame(card, bg=self.colors['surface'])
            start_frame.pack(fill='x', padx=15, pady=(10, 15))

            self.start_button = tk.Button(start_frame, text="🚀 开始优化",
                                        command=self.start_optimization,
                                        font=('Microsoft YaHei UI', 11, 'bold'),
                                        bg=self.colors['accent'], fg='white',
                                        relief='flat', height=2)
            self.start_button.pack(fill='x')

        except Exception as e:
            print(f"❌ 渠道管理区域创建失败: {e}")

    def create_monitoring_section(self):
        """创建监控区域"""
        try:
            # 监控卡片
            card = tk.Frame(self.right_frame, bg=self.colors['surface'], relief='solid', bd=1)
            card.grid(row=0, column=0, sticky='ew', pady=(0, 10))

            # 标题
            title_frame = tk.Frame(card, bg=self.colors['surface'])
            title_frame.pack(fill='x', padx=15, pady=(15, 10))

            tk.Label(title_frame, text="📊 实时监控",
                    bg=self.colors['surface'], fg=self.colors['primary'],
                    font=('Microsoft YaHei UI', 12, 'bold')).pack(anchor='w')

            # 监控指标网格
            metrics_frame = tk.Frame(card, bg=self.colors['surface'])
            metrics_frame.pack(fill='x', padx=15, pady=(0, 10))

            # 配置网格权重
            metrics_frame.grid_columnconfigure(0, weight=1)
            metrics_frame.grid_columnconfigure(1, weight=1)

            # 创建监控指标
            self.create_metric_display(metrics_frame, "基准KPI", self.baseline_kpi_var, 0, 0)
            self.create_metric_display(metrics_frame, "当前最优", self.current_best_kpi_var, 0, 1)
            self.create_metric_display(metrics_frame, "提升幅度", self.improvement_var, 1, 0)
            self.create_metric_display(metrics_frame, "进度", self.progress_var, 1, 1)

            # 状态显示
            status_frame = tk.Frame(card, bg=self.colors['surface'])
            status_frame.pack(fill='x', padx=15, pady=(10, 15))

            tk.Label(status_frame, text="状态:",
                    bg=self.colors['surface'], fg=self.colors['text'],
                    font=('Microsoft YaHei UI', 9)).pack(anchor='w')

            self.status_label = tk.Label(status_frame, textvariable=self.status_var,
                                       bg=self.colors['surface'], fg=self.colors['primary'],
                                       font=('Microsoft YaHei UI', 10, 'bold'))
            self.status_label.pack(anchor='w', pady=(5, 0))

        except Exception as e:
            print(f"❌ 监控区域创建失败: {e}")

    def create_metric_display(self, parent, label, variable, row, col):
        """创建监控指标显示"""
        try:
            metric_frame = tk.Frame(parent, bg=self.colors['surface'], relief='solid', bd=1)
            metric_frame.grid(row=row, column=col, padx=5, pady=5, sticky='ew')

            tk.Label(metric_frame, text=label,
                    bg=self.colors['surface'], fg=self.colors['text'],
                    font=('Microsoft YaHei UI', 9)).pack(anchor='w', padx=10, pady=(10, 5))

            value_label = tk.Label(metric_frame, textvariable=variable,
                                 bg=self.colors['surface'], fg=self.colors['primary'],
                                 font=('Microsoft YaHei UI', 12, 'bold'))
            value_label.pack(anchor='w', padx=10, pady=(0, 10))

        except Exception as e:
            print(f"监控指标创建失败: {e}")

    def create_results_section(self):
        """创建结果区域"""
        try:
            # 结果卡片
            card = tk.Frame(self.right_frame, bg=self.colors['surface'], relief='solid', bd=1)
            card.grid(row=1, column=0, sticky='nsew', pady=(0, 10))

            # 标题
            title_frame = tk.Frame(card, bg=self.colors['surface'])
            title_frame.pack(fill='x', padx=15, pady=(15, 10))

            tk.Label(title_frame, text="🏆 优化结果",
                    bg=self.colors['surface'], fg=self.colors['primary'],
                    font=('Microsoft YaHei UI', 12, 'bold')).pack(anchor='w')

            # 结果显示区域
            self.results_text = scrolledtext.ScrolledText(
                card,
                wrap=tk.WORD,
                font=('Microsoft YaHei UI', 9),
                bg='white',
                fg=self.colors['text']
            )
            self.results_text.pack(fill='both', expand=True, padx=15, pady=(0, 15))

        except Exception as e:
            print(f"❌ 结果区域创建失败: {e}")

    def create_log_section(self):
        """创建日志区域"""
        try:
            # 日志卡片
            card = tk.Frame(self.right_frame, bg=self.colors['surface'], relief='solid', bd=1)
            card.grid(row=2, column=0, sticky='ew')

            # 标题
            title_frame = tk.Frame(card, bg=self.colors['surface'])
            title_frame.pack(fill='x', padx=15, pady=(15, 10))

            tk.Label(title_frame, text="📝 运行日志",
                    bg=self.colors['surface'], fg=self.colors['primary'],
                    font=('Microsoft YaHei UI', 12, 'bold')).pack(anchor='w')

            # 日志显示区域
            self.log_text = scrolledtext.ScrolledText(
                card,
                wrap=tk.WORD,
                height=6,
                font=('Consolas', 8),
                bg='#2C3E50',
                fg='#ECF0F1'
            )
            self.log_text.pack(fill='x', padx=15, pady=(0, 15))

        except Exception as e:
            print(f"❌ 日志区域创建失败: {e}")

    def on_window_resize(self, event):
        """窗口大小改变时的处理"""
        try:
            if event.widget == self.root:
                # 更新Canvas的滚动区域
                if hasattr(self, 'left_canvas'):
                    self.left_canvas.configure(scrollregion=self.left_canvas.bbox("all"))
                    
                # 更新Canvas的宽度以适应窗口
                if hasattr(self, 'left_scrollable_frame'):
                    canvas_width = self.left_canvas.winfo_width()
                    if canvas_width > 1:
                        self.left_canvas.itemconfig(
                            self.left_canvas.find_all()[0], 
                            width=canvas_width
                        )
                        
        except Exception as e:
            pass  # 忽略resize过程中的错误
            
    def bind_mousewheel(self, widget):
        """绑定鼠标滚轮事件"""
        def _on_mousewheel(event):
            try:
                widget.yview_scroll(int(-1*(event.delta/120)), "units")
            except:
                pass
        
        widget.bind("<MouseWheel>", _on_mousewheel)
        
    def browse_file(self):
        """浏览文件"""
        try:
            filename = filedialog.askopenfilename(
                title="选择Excel文件",
                filetypes=[("Excel files", "*.xlsx *.xls")]
            )
            if filename:
                self.file_path_var.set(filename)
                self.load_sheet_names()
                self.add_log(f"选择文件: {os.path.basename(filename)}")
        except Exception as e:
            self.add_log(f"文件选择失败: {e}")
            
    def load_sheet_names(self):
        """加载工作表名称"""
        try:
            # 先清理可能存在的Excel进程
            self.cleanup_excel()

            # 使用win32com读取工作表名称
            excel_app = win32com.client.Dispatch("Excel.Application")
            excel_app.Visible = False
            excel_app.DisplayAlerts = False

            workbook = excel_app.Workbooks.Open(self.file_path_var.get())
            sheet_names = [sheet.Name for sheet in workbook.Worksheets]

            self.sheet_combo['values'] = sheet_names
            if sheet_names:
                self.sheet_name_var.set(sheet_names[0])

            workbook.Close(SaveChanges=False)
            excel_app.Quit()

            # 确保Excel进程完全关闭
            import time
            time.sleep(0.5)

            self.add_log(f"读取到 {len(sheet_names)} 个工作表")

        except Exception as e:
            self.add_log(f"读取工作表失败: {e}")
            # 尝试使用openpyxl作为备用方案
            try:
                import openpyxl
                workbook = openpyxl.load_workbook(self.file_path_var.get(), read_only=True)
                sheet_names = workbook.sheetnames
                self.sheet_combo['values'] = sheet_names
                if sheet_names:
                    self.sheet_name_var.set(sheet_names[0])
                workbook.close()
                self.add_log(f"使用openpyxl读取到 {len(sheet_names)} 个工作表")
            except:
                messagebox.showerror("错误", f"无法读取Excel文件: {str(e)}")

    def read_channels(self):
        """读取渠道信息"""
        try:
            # 验证必要参数
            if not self.file_path_var.get():
                messagebox.showerror("错误", "请先选择Excel文件")
                return

            if not self.sheet_name_var.get():
                messagebox.showerror("错误", "请选择工作表")
                return

            if not all([self.data_start_col_var.get(), self.data_end_col_var.get(),
                       self.channel_row_var.get()]):
                messagebox.showerror("错误", "请填写完整的数据范围信息")
                return

            self.add_log("开始读取渠道信息...")

            # 连接Excel
            self.excel_app = win32com.client.Dispatch("Excel.Application")
            self.excel_app.Visible = False
            self.excel_app.DisplayAlerts = False

            self.workbook = self.excel_app.Workbooks.Open(self.file_path_var.get())
            self.worksheet = self.workbook.Worksheets(self.sheet_name_var.get())

            # 读取渠道名称
            channel_row = int(self.channel_row_var.get())
            start_col = self.column_letter_to_number(self.data_start_col_var.get())
            end_col = self.column_letter_to_number(self.data_end_col_var.get())

            self.channels_data = []
            for col in range(start_col, end_col + 1):
                cell_value = self.worksheet.Cells(channel_row, col).Value
                if cell_value and str(cell_value).strip():
                    self.channels_data.append(str(cell_value).strip())

            if not self.channels_data:
                messagebox.showerror("错误", "未读取到任何渠道信息")
                return

            self.add_log(f"✅ 成功读取 {len(self.channels_data)} 个渠道")

            # 显示渠道分类界面
            self.display_channel_classification()

            # 关闭Excel连接
            self.workbook.Close(SaveChanges=False)
            self.excel_app.Quit()
            self.excel_app = None
            self.workbook = None

        except Exception as e:
            self.add_log(f"❌ 读取渠道失败: {str(e)}")
            messagebox.showerror("错误", f"读取渠道信息失败: {str(e)}")
            self.cleanup_excel()

    def display_channel_classification(self):
        """显示渠道分类界面"""
        try:
            # 清除现有内容
            for widget in self.channels_container.winfo_children():
                widget.destroy()

            if not self.channels_data:
                return

            # 创建标题
            header_frame = tk.Frame(self.channels_container, bg=self.colors['surface'])
            header_frame.pack(fill='x', pady=(0, 10))

            tk.Label(header_frame, text="渠道分类与约束设置",
                    bg=self.colors['surface'], fg=self.colors['text'],
                    font=('Microsoft YaHei UI', 10, 'bold')).pack(anchor='w')

            # 创建滚动框架
            canvas = tk.Canvas(self.channels_container, height=200, bg=self.colors['surface'], highlightthickness=0)
            scrollbar = ttk.Scrollbar(self.channels_container, orient="vertical", command=canvas.yview)
            scrollable_frame = tk.Frame(canvas, bg=self.colors['surface'])

            scrollable_frame.bind(
                "<Configure>",
                lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
            )

            canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)

            # 渠道类型选项
            channel_type_options = ["数字媒体", "社交媒体", "传统媒体", "户外媒体", "搜索引擎", "其他"]

            # 为每个渠道创建编辑行
            self.channel_widgets = {}
            for i, channel in enumerate(self.channels_data):
                row_frame = tk.Frame(scrollable_frame, bg=self.colors['surface'], relief='solid', bd=1)
                row_frame.pack(fill='x', padx=2, pady=2)

                # 渠道名称
                name_label = tk.Label(row_frame, text=channel,
                                    bg=self.colors['surface'], fg=self.colors['text'],
                                    font=('Microsoft YaHei UI', 9), width=12, anchor='w')
                name_label.pack(side='left', padx=(5, 10), pady=5)

                # 渠道类型选择
                type_var = tk.StringVar(value=self.channel_types.get(channel, "数字媒体"))
                type_combo = ttk.Combobox(row_frame, textvariable=type_var,
                                         values=channel_type_options,
                                         width=10, state='readonly',
                                         font=('Microsoft YaHei UI', 8))
                type_combo.pack(side='left', padx=(0, 10), pady=5)

                # 约束设置
                constraint_frame = tk.Frame(row_frame, bg=self.colors['surface'])
                constraint_frame.pack(side='left', fill='x', expand=True, pady=5)

                tk.Label(constraint_frame, text="比例:",
                        bg=self.colors['surface'], fg=self.colors['text'],
                        font=('Microsoft YaHei UI', 8)).pack(side='left', padx=(0, 2))

                min_var = tk.StringVar(value="2")
                max_var = tk.StringVar(value="40")

                min_entry = tk.Entry(constraint_frame, textvariable=min_var,
                                   font=('Microsoft YaHei UI', 8), width=4)
                min_entry.pack(side='left', padx=(0, 2))

                tk.Label(constraint_frame, text="%-",
                        bg=self.colors['surface'], fg=self.colors['text'],
                        font=('Microsoft YaHei UI', 8)).pack(side='left', padx=1)

                max_entry = tk.Entry(constraint_frame, textvariable=max_var,
                                   font=('Microsoft YaHei UI', 8), width=4)
                max_entry.pack(side='left', padx=(1, 2))

                tk.Label(constraint_frame, text="%",
                        bg=self.colors['surface'], fg=self.colors['text'],
                        font=('Microsoft YaHei UI', 8)).pack(side='left')

                # 保存控件引用
                self.channel_widgets[channel] = {
                    'type_var': type_var,
                    'min_var': min_var,
                    'max_var': max_var
                }

            canvas.pack(side="left", fill="both", expand=True)
            scrollbar.pack(side="right", fill="y")

            # 绑定鼠标滚轮
            self.bind_mousewheel(canvas)

            self.add_log("✅ 渠道分类界面已生成")

        except Exception as e:
            self.add_log(f"❌ 渠道分类界面创建失败: {str(e)}")

    def column_letter_to_number(self, letter):
        """将列字母转换为数字"""
        try:
            if isinstance(letter, int):
                return letter

            if letter.isdigit():
                return int(letter)

            letter = letter.upper()
            result = 0
            for char in letter:
                result = result * 26 + (ord(char) - ord('A') + 1)
            return result
        except:
            return 1

    def start_optimization(self):
        """开始优化"""
        try:
            # 验证参数
            if not self.validate_optimization_params():
                return

            # 收集配置
            config = self.collect_optimization_config()

            # 禁用开始按钮
            self.start_button.configure(state='disabled', text="优化中...")

            # 清空结果和重置监控
            self.results_text.delete(1.0, tk.END)
            self.baseline_kpi_var.set("0")
            self.current_best_kpi_var.set("0")
            self.improvement_var.set("0%")
            self.progress_var.set("0%")
            self.status_var.set("准备中...")

            # 在新线程中运行优化
            self.optimization_thread = threading.Thread(
                target=self.run_optimization_thread,
                args=(config,)
            )
            self.optimization_thread.daemon = True
            self.optimization_thread.start()

        except Exception as e:
            self.add_log(f"❌ 启动优化失败: {str(e)}")
            self.start_button.configure(state='normal', text="🚀 开始优化")

    def validate_optimization_params(self):
        """验证优化参数"""
        try:
            # 检查基本参数
            if not self.channels_data:
                messagebox.showerror("错误", "请先读取渠道信息")
                return False

            if not self.total_budget_var.get():
                messagebox.showerror("错误", "请输入总预算")
                return False

            try:
                budget = float(self.total_budget_var.get())
                if budget <= 0:
                    raise ValueError("预算必须大于0")
            except ValueError:
                messagebox.showerror("错误", "请输入有效的总预算")
                return False

            # 检查数据范围
            required_fields = [
                (self.data_start_row_var.get(), "数据起始行"),
                (self.data_end_row_var.get(), "数据结束行"),
                (self.data_start_col_var.get(), "数据起始列"),
                (self.data_end_col_var.get(), "数据结束列")
            ]

            for field_value, field_name in required_fields:
                if not field_value:
                    messagebox.showerror("错误", f"请填写{field_name}")
                    return False

            return True

        except Exception as e:
            messagebox.showerror("错误", f"参数验证失败: {str(e)}")
            return False

    def collect_optimization_config(self):
        """收集优化配置"""
        try:
            # 收集渠道类型和约束
            channel_types = {}
            channel_constraints = {}

            for channel, widgets in self.channel_widgets.items():
                channel_types[channel] = widgets['type_var'].get()

                try:
                    min_ratio = float(widgets['min_var'].get()) / 100.0
                    max_ratio = float(widgets['max_var'].get()) / 100.0

                    # 确保约束合理
                    min_ratio = max(0.0, min(min_ratio, 1.0))
                    max_ratio = max(min_ratio, min(max_ratio, 1.0))

                    channel_constraints[channel] = {
                        'min': min_ratio,
                        'max': max_ratio
                    }
                except ValueError:
                    # 使用默认约束
                    channel_constraints[channel] = {'min': 0.02, 'max': 0.40}

            # 基准比例配置
            baseline_config = {
                'method': self.baseline_method_var.get()
            }

            if baseline_config['method'] == 'data_table':
                baseline_config.update({
                    'start_row': int(self.baseline_start_row_var.get() or "1"),
                    'end_row': int(self.baseline_end_row_var.get() or "1"),
                    'start_col': self.baseline_start_col_var.get() or "A",
                    'end_col': self.baseline_end_col_var.get() or "Z"
                })

            config = {
                'file_path': self.file_path_var.get(),
                'sheet_name': self.sheet_name_var.get(),
                'data_range': {
                    'start_row': int(self.data_start_row_var.get()),
                    'end_row': int(self.data_end_row_var.get()),
                    'start_col': self.data_start_col_var.get(),
                    'end_col': self.data_end_col_var.get(),
                    'channel_row': int(self.channel_row_var.get())
                },
                'baseline_config': baseline_config,
                'optimization_config': {
                    'total_budget': float(self.total_budget_var.get()),
                    'num_solutions': int(self.num_solutions_var.get() or "5"),
                    'iterations': int(self.iterations_var.get() or "100"),
                    'kpi_type': self.kpi_type_var.get()
                },
                'channel_constraints': channel_constraints,
                'channel_types': channel_types,
                'channels': self.channels_data
            }

            return config

        except Exception as e:
            self.add_log(f"❌ 配置收集失败: {str(e)}")
            return None

    def run_optimization_thread(self, config):
        """在线程中运行优化"""
        try:
            self.optimization_running = True

            # 运行优化算法
            results = self.run_optimization_algorithm(config)

            if results:
                # 在主线程中更新界面
                self.root.after(0, self.on_optimization_complete, results)
            else:
                self.root.after(0, self.on_optimization_error, "优化过程中发生错误")

        except Exception as e:
            self.root.after(0, self.on_optimization_error, str(e))
        finally:
            self.optimization_running = False

    def run_optimization_algorithm(self, config):
        """运行优化算法"""
        try:
            self.update_status("连接Excel...")

            # 连接Excel
            excel_app = win32com.client.Dispatch("Excel.Application")
            excel_app.Visible = False
            excel_app.DisplayAlerts = False

            workbook = excel_app.Workbooks.Open(config['file_path'])
            worksheet = workbook.Worksheets(config['sheet_name'])

            # 计算基准KPI
            self.update_status("计算基准KPI...")
            baseline_kpi = self.calculate_baseline_kpi(worksheet, config)

            self.root.after(0, lambda: self.baseline_kpi_var.set(f"{baseline_kpi:,.2f}"))

            # 运行优化迭代
            self.update_status("开始优化迭代...")
            best_kpi = baseline_kpi
            best_solution = None
            all_solutions = []

            iterations = config['optimization_config']['iterations']

            for i in range(iterations):
                if not self.optimization_running:
                    break

                # 生成随机解决方案
                solution = self.generate_random_solution(config)

                # 计算KPI
                kpi = self.calculate_solution_kpi(worksheet, solution, config)

                # 更新最优解
                if kpi > best_kpi:
                    best_kpi = kpi
                    best_solution = solution

                    # 实时更新监控
                    improvement = ((kpi - baseline_kpi) / baseline_kpi * 100) if baseline_kpi > 0 else 0

                    self.root.after(0, lambda k=kpi, imp=improvement, prog=i+1: self.update_monitoring(k, imp, prog, iterations))

                # 保存解决方案
                all_solutions.append({
                    'solution': solution,
                    'kpi': kpi,
                    'iteration': i + 1
                })

                # 短暂延迟
                time.sleep(0.01)

            # 排序解决方案
            all_solutions.sort(key=lambda x: x['kpi'], reverse=True)
            num_solutions = config['optimization_config']['num_solutions']
            all_solutions = all_solutions[:num_solutions]

            # 关闭Excel
            workbook.Close(SaveChanges=False)
            excel_app.Quit()

            self.update_status("优化完成")

            return {
                'baseline_kpi': baseline_kpi,
                'best_kpi': best_kpi,
                'best_solution': best_solution,
                'all_solutions': all_solutions,
                'channels': config['channels']
            }

        except Exception as e:
            self.add_log(f"❌ 优化算法失败: {str(e)}")
            return None

    def update_status(self, status):
        """更新状态"""
        self.root.after(0, lambda: self.status_var.set(status))
        self.root.after(0, lambda: self.add_log(status))

    def update_monitoring(self, kpi, improvement, progress, total):
        """更新监控数据"""
        self.current_best_kpi_var.set(f"{kpi:,.2f}")
        self.improvement_var.set(f"{improvement:.1f}%")
        self.progress_var.set(f"{int(progress/total*100)}%")

    def calculate_baseline_kpi(self, worksheet, config):
        """计算基准KPI"""
        try:
            # 根据基准方法计算
            method = config['baseline_config']['method']
            channels = config['channels']
            total_budget = config['optimization_config']['total_budget']

            if method == 'average':
                # 平均分配
                budget_per_channel = total_budget / len(channels)
                baseline_budgets = {channel: budget_per_channel for channel in channels}
            elif method == 'data_table':
                # 从数据表计算
                baseline_budgets = self.calculate_baseline_from_data(worksheet, config)
            else:
                # 手动设置（暂时使用平均分配）
                budget_per_channel = total_budget / len(channels)
                baseline_budgets = {channel: budget_per_channel for channel in channels}

            # 写入预算并计算KPI
            return self.calculate_kpi_for_budgets(worksheet, baseline_budgets, config)

        except Exception as e:
            self.add_log(f"基准KPI计算失败: {e}")
            return 0

    def calculate_baseline_from_data(self, worksheet, config):
        """从数据表计算基准比例"""
        try:
            baseline_config = config['baseline_config']
            start_row = baseline_config['start_row']
            end_row = baseline_config['end_row']
            start_col = self.column_letter_to_number(baseline_config['start_col'])
            end_col = self.column_letter_to_number(baseline_config['end_col'])

            channels = config['channels']
            total_budget = config['optimization_config']['total_budget']

            # 读取数据并计算总和
            channel_totals = {}
            total_sum = 0

            for col_idx, channel in enumerate(channels):
                col = start_col + col_idx
                if col > end_col:
                    break

                channel_sum = 0
                for row in range(start_row, end_row + 1):
                    cell_value = worksheet.Cells(row, col).Value
                    if cell_value and isinstance(cell_value, (int, float)):
                        channel_sum += cell_value

                channel_totals[channel] = channel_sum
                total_sum += channel_sum

            # 计算预算分配
            baseline_budgets = {}
            if total_sum > 0:
                for channel in channels:
                    ratio = channel_totals.get(channel, 0) / total_sum
                    baseline_budgets[channel] = total_budget * ratio
            else:
                # 如果总和为0，使用平均分配
                budget_per_channel = total_budget / len(channels)
                baseline_budgets = {channel: budget_per_channel for channel in channels}

            return baseline_budgets

        except Exception as e:
            self.add_log(f"数据表基准计算失败: {e}")
            # 返回平均分配
            budget_per_channel = config['optimization_config']['total_budget'] / len(config['channels'])
            return {channel: budget_per_channel for channel in config['channels']}

    def calculate_kpi_for_budgets(self, worksheet, budgets, config):
        """为给定预算计算KPI"""
        try:
            # 写入预算到Excel
            data_range = config['data_range']
            start_col = self.column_letter_to_number(data_range['start_col'])
            budget_row = data_range['start_row'] - 1  # 假设预算在数据上一行

            channels = config['channels']
            for col_idx, channel in enumerate(channels):
                col = start_col + col_idx
                budget = budgets.get(channel, 0)
                worksheet.Cells(budget_row, col).Value = budget

            # 计算KPI
            return self.calculate_kpi(worksheet, config)

        except Exception as e:
            self.add_log(f"KPI计算失败: {e}")
            return 0

    def calculate_kpi(self, worksheet, config):
        """计算KPI值"""
        try:
            kpi_type = config['optimization_config']['kpi_type']
            data_range = config['data_range']
            start_row = data_range['start_row']
            end_row = data_range['end_row']
            start_col = self.column_letter_to_number(data_range['start_col'])
            end_col = self.column_letter_to_number(data_range['end_col'])

            if kpi_type == "BHT":
                # BHT: 计算平均值
                total_kpi = 0
                valid_rows = 0

                for row in range(start_row, end_row + 1):
                    row_sum = 0
                    valid_cols = 0

                    for col in range(start_col, end_col + 1):
                        cell_value = worksheet.Cells(row, col).Value
                        if cell_value and isinstance(cell_value, (int, float)):
                            row_sum += cell_value
                            valid_cols += 1

                    if valid_cols > 0:
                        total_kpi += row_sum / valid_cols
                        valid_rows += 1

                return total_kpi / valid_rows if valid_rows > 0 else 0

            else:
                # Non-BHT: 计算总和
                total_kpi = 0

                for row in range(start_row, end_row + 1):
                    for col in range(start_col, end_col + 1):
                        cell_value = worksheet.Cells(row, col).Value
                        if cell_value and isinstance(cell_value, (int, float)):
                            total_kpi += cell_value

                return total_kpi

        except Exception as e:
            self.add_log(f"KPI计算失败: {e}")
            return 0

    def generate_random_solution(self, config):
        """生成随机解决方案（考虑约束）"""
        try:
            channels = config['channels']
            constraints = config['channel_constraints']
            total_budget = config['optimization_config']['total_budget']

            max_attempts = 1000

            for attempt in range(max_attempts):
                # 生成随机比例
                ratios = {}
                remaining = 1.0

                # 按约束生成比例
                for i, channel in enumerate(channels[:-1]):
                    constraint = constraints.get(channel, {'min': 0.01, 'max': 0.99})
                    min_ratio = constraint['min']
                    max_ratio = constraint['max']

                    # 确保不超过剩余比例
                    max_ratio = min(max_ratio, remaining - 0.01 * (len(channels) - i - 1))
                    min_ratio = min(min_ratio, max_ratio)

                    if max_ratio <= min_ratio:
                        ratio = min_ratio
                    else:
                        ratio = random.uniform(min_ratio, max_ratio)

                    ratios[channel] = ratio
                    remaining -= ratio

                # 最后一个渠道获得剩余比例
                last_channel = channels[-1]
                ratios[last_channel] = remaining

                # 验证约束
                if self.validate_solution_constraints(ratios, constraints):
                    # 转换为预算
                    budgets = {channel: total_budget * ratio for channel, ratio in ratios.items()}
                    return budgets

            # 如果无法生成有效解，返回平均分配
            self.add_log("⚠️ 无法生成满足约束的随机解，使用平均分配")
            budget_per_channel = total_budget / len(channels)
            return {channel: budget_per_channel for channel in channels}

        except Exception as e:
            self.add_log(f"随机解生成失败: {e}")
            budget_per_channel = config['optimization_config']['total_budget'] / len(config['channels'])
            return {channel: budget_per_channel for channel in config['channels']}

    def validate_solution_constraints(self, ratios, constraints):
        """验证解决方案约束"""
        try:
            # 检查比例总和
            total_ratio = sum(ratios.values())
            if abs(total_ratio - 1.0) > 0.001:
                return False

            # 检查每个渠道的约束
            for channel, ratio in ratios.items():
                constraint = constraints.get(channel, {'min': 0, 'max': 1})
                min_ratio = constraint['min']
                max_ratio = constraint['max']

                if ratio < min_ratio or ratio > max_ratio:
                    return False

            return True

        except Exception as e:
            return False

    def calculate_solution_kpi(self, worksheet, solution, config):
        """计算解决方案的KPI"""
        try:
            return self.calculate_kpi_for_budgets(worksheet, solution, config)
        except Exception as e:
            self.add_log(f"解决方案KPI计算失败: {e}")
            return 0

    def on_optimization_complete(self, results):
        """优化完成处理"""
        try:
            self.add_log("✅ 优化完成！")

            # 显示结果
            self.display_optimization_results(results)

            # 恢复开始按钮
            self.start_button.configure(state='normal', text="🚀 开始优化")

        except Exception as e:
            self.add_log(f"❌ 处理优化结果失败: {str(e)}")
            self.start_button.configure(state='normal', text="🚀 开始优化")

    def on_optimization_error(self, error_message):
        """优化错误处理"""
        self.add_log(f"❌ 优化失败: {error_message}")
        self.start_button.configure(state='normal', text="🚀 开始优化")
        messagebox.showerror("优化失败", f"优化过程中发生错误:\n{error_message}")

    def display_optimization_results(self, results):
        """显示优化结果"""
        try:
            self.results_text.delete(1.0, tk.END)

            # 基本信息
            baseline_kpi = results['baseline_kpi']
            best_kpi = results['best_kpi']
            improvement = ((best_kpi - baseline_kpi) / baseline_kpi * 100) if baseline_kpi > 0 else 0

            self.results_text.insert(tk.END, "🏆 优化结果摘要\n", "title")
            self.results_text.insert(tk.END, "=" * 40 + "\n\n", "separator")

            self.results_text.insert(tk.END, f"📈 基准KPI: {baseline_kpi:,.2f}\n", "normal")
            self.results_text.insert(tk.END, f"🎯 最优KPI: {best_kpi:,.2f}\n", "highlight")
            self.results_text.insert(tk.END, f"📊 提升幅度: {improvement:.2f}%\n\n", "success")

            # 显示前3个最优方案
            solutions = results['all_solutions'][:3]

            self.results_text.insert(tk.END, "🏅 最优方案:\n", "subtitle")
            self.results_text.insert(tk.END, "-" * 30 + "\n", "separator")

            for i, sol in enumerate(solutions, 1):
                kpi = sol['kpi']
                improvement = ((kpi - baseline_kpi) / baseline_kpi * 100) if baseline_kpi > 0 else 0

                self.results_text.insert(tk.END, f"\n方案 {i}: KPI {kpi:,.2f} (提升 {improvement:.1f}%)\n", "subtitle")

                # 显示主要渠道分配
                budgets = sol['solution']
                total_budget = sum(budgets.values())

                sorted_budgets = sorted(budgets.items(), key=lambda x: x[1], reverse=True)[:3]

                for channel, budget in sorted_budgets:
                    ratio = (budget / total_budget * 100) if total_budget > 0 else 0
                    self.results_text.insert(tk.END, f"  {channel}: {budget:,.0f} ({ratio:.1f}%)\n", "normal")

            self.results_text.insert(tk.END, f"\n💾 共生成 {len(results['all_solutions'])} 个优化方案\n", "info")

            # 配置文本样式
            self.configure_text_tags()

        except Exception as e:
            self.add_log(f"❌ 结果显示失败: {str(e)}")

    def configure_text_tags(self):
        """配置文本标签样式"""
        try:
            self.results_text.tag_configure("title",
                                            font=('Microsoft YaHei UI', 12, 'bold'),
                                            foreground=self.colors['primary'])
            self.results_text.tag_configure("subtitle",
                                            font=('Microsoft YaHei UI', 10, 'bold'),
                                            foreground=self.colors['secondary'])
            self.results_text.tag_configure("highlight",
                                            font=('Microsoft YaHei UI', 10, 'bold'),
                                            foreground=self.colors['accent'])
            self.results_text.tag_configure("success",
                                            font=('Microsoft YaHei UI', 10, 'bold'),
                                            foreground=self.colors['success'])
            self.results_text.tag_configure("normal",
                                            font=('Microsoft YaHei UI', 9),
                                            foreground=self.colors['text'])
            self.results_text.tag_configure("info",
                                            font=('Microsoft YaHei UI', 9),
                                            foreground=self.colors['text_light'])
            self.results_text.tag_configure("separator",
                                            foreground=self.colors['border'])
        except:
            pass

    def create_simple_layout(self):
        """创建简化布局（备用）"""
        try:
            # 简化的界面
            tk.Label(self.main_frame, text="媒体预算优化工具 v4.0 - 简化模式",
                    font=('Microsoft YaHei UI', 16, 'bold'),
                    bg=self.colors['background']).pack(pady=50)

            tk.Label(self.main_frame, text="程序正在简化模式下运行",
                    font=('Microsoft YaHei UI', 12),
                    bg=self.colors['background']).pack(pady=20)

        except Exception as e:
            print(f"简化布局创建失败: {e}")

    def add_log(self, message):
        """添加日志"""
        try:
            timestamp = datetime.now().strftime("%H:%M:%S")
            log_message = f"[{timestamp}] {message}\n"
            
            if hasattr(self, 'log_text'):
                self.log_text.insert(tk.END, log_message)
                self.log_text.see(tk.END)
                self.root.update_idletasks()
            else:
                print(log_message.strip())
                
        except Exception as e:
            print(f"日志添加失败: {e}")
            
    def on_closing(self):
        """窗口关闭时的处理"""
        try:
            if self.optimization_running:
                if messagebox.askokcancel("退出", "优化正在进行中，确定要退出吗？"):
                    self.optimization_running = False
                    self.cleanup_excel()
                    self.root.destroy()
            else:
                self.cleanup_excel()
                self.root.destroy()
        except Exception as e:
            print(f"关闭处理失败: {e}")
            self.root.destroy()
            
    def cleanup_excel(self):
        """清理Excel连接"""
        try:
            if self.workbook:
                self.workbook.Close(SaveChanges=False)
            if self.excel_app:
                self.excel_app.Quit()
        except:
            pass
            
    def run(self):
        """运行应用"""
        try:
            print("启动主循环...")
            self.root.mainloop()
            print("程序正常退出")
        except Exception as e:
            print(f"运行失败: {e}")
            traceback.print_exc()

def main():
    """主函数"""
    try:
        print("开始启动媒体预算优化工具 v4.0 完整版...")
        app = MediaOptimizerV4Complete()
        app.run()
    except Exception as e:
        print(f"程序启动失败: {e}")
        traceback.print_exc()
        input("按回车键退出...")

if __name__ == "__main__":
    main()
