# 媒体预算优化工具 v4.1 - 改进版使用说明

## 🎉 v4.1 新功能

### 1. ✅ 输出数据范围配置
- **新增功能**: 可以指定优化结果写入Excel的具体位置
- **预算输出行**: 设置优化后的预算分配写入哪一行
- **结果数据范围**: 指定结果数据的行列范围
- **用途**: 确保优化结果准确写入到指定的Excel位置

### 2. ✅ 统一滚动界面
- **移除左右分栏**: 不再有严格的左右面板分离
- **统一滚动**: 配置和监控区域可以一起垂直滚动
- **更好的空间利用**: 所有功能区域在一个统一的界面中
- **流畅体验**: 鼠标滚轮在任何位置都能滚动整个界面

### 3. ✅ 增强的窗口响应性
- **完全自适应**: 窗口最大化时所有组件正确调整大小
- **网格权重分布**: 组件按比例扩展以填充可用空间
- **动态调整**: 窗口大小变化时实时调整布局
- **支持范围**: 1200x700 到全屏分辨率

### 4. ✅ 版本管理
- **清晰版本号**: v4.1 明确标识
- **精简分发**: 只包含必要文件
- **无冗余文件**: 移除测试文件和开发工件

## 🚀 快速启动

### 推荐方式
双击运行 **"start_v4_1.bat"**
- 自动检查Python环境
- 自动安装缺失依赖
- 启动v4.1改进版界面

### 手动方式
```
python media_optimizer_v4_1.py
```

## 📊 功能区域说明

### 1. 📁 文件配置
- **Excel文件选择**: 浏览并选择要处理的Excel文件
- **工作表选择**: 从下拉列表中选择具体的工作表

### 2. 📊 输入数据范围配置
- **数据行范围**: 设置包含原始数据的行范围（如：2到10）
- **数据列范围**: 设置包含原始数据的列范围（如：C到R）
- **渠道名称行**: 设置包含渠道名称的行号（如：1）

### 3. 📤 输出结果范围配置 (新增)
- **预算输出行**: 指定优化后的预算分配写入哪一行
- **结果数据行范围**: 设置结果数据的行范围
- **结果数据列范围**: 设置结果数据的列范围
- **用途**: 确保优化结果不会覆盖原始数据

### 4. ⚖️ 基准比例设置
- **手动设置**: 手动输入各渠道的基准比例
- **数据表计算**: 从Excel指定范围自动计算历史比例
- **平均分配**: 所有渠道平均分配预算

### 5. ⚙️ 优化配置
- **总预算**: 设置总的预算金额
- **KPI类型**: 选择Non-BHT（求和）或BHT（平均）
- **生成方案数**: 设置要生成的优化方案数量
- **迭代次数**: 设置优化算法的迭代次数

### 6. 🎯 渠道管理
- **读取渠道信息**: 从Excel自动读取渠道列表
- **渠道分类**: 设置每个渠道的媒体类型
- **约束设置**: 为每个渠道设置比例范围约束

### 7. 📊 实时监控
- **基准KPI**: 显示基准情况下的KPI值
- **当前最优**: 显示当前找到的最优KPI值
- **提升幅度**: 显示相对于基准的提升百分比
- **进度**: 显示优化进度

### 8. 🏆 优化结果
- **结果摘要**: 显示优化结果的关键信息
- **方案对比**: 展示最优的几个方案
- **详细分配**: 显示各渠道的预算分配

### 9. 📝 运行日志
- **操作记录**: 记录所有操作和状态变化
- **错误信息**: 显示详细的错误和警告信息
- **调试信息**: 帮助诊断问题的技术信息

## 💡 使用流程

### 第一步：文件准备
1. 准备Excel文件，确保数据格式正确
2. 确保渠道名称在指定行
3. 确保数据在连续的行列范围内

### 第二步：配置数据范围
1. 选择Excel文件和工作表
2. 设置输入数据的行列范围
3. **新功能**: 设置输出结果的行列范围
4. 指定渠道名称所在行

### 第三步：配置基准和优化参数
1. 选择基准比例计算方式
2. 设置总预算和KPI类型
3. 配置生成方案数和迭代次数

### 第四步：管理渠道
1. 点击"读取渠道信息"
2. 设置每个渠道的类型和约束
3. 确认约束设置合理

### 第五步：执行优化
1. 点击"开始优化"
2. 观察实时监控数据
3. 等待优化完成

### 第六步：查看和保存结果
1. 在结果区域查看优化方案
2. 确认结果满意
3. 结果会自动写入到指定的Excel位置

## ⚠️ 注意事项

### 输出范围配置
- **避免覆盖**: 确保输出范围不会覆盖原始数据
- **范围匹配**: 输出列范围应与输入列范围匹配
- **预算行**: 预算输出行应在数据范围之外

### 窗口使用
- **滚动操作**: 使用鼠标滚轮在任何位置滚动
- **窗口调整**: 可以自由调整窗口大小，界面会自动适应
- **最大化**: 支持窗口最大化，所有组件会正确扩展

### 数据格式
- **数值格式**: 确保Excel中的数据为数值格式
- **连续范围**: 数据应在连续的行列范围内
- **渠道名称**: 渠道名称应清晰且无重复

## 🔧 故障排除

### 界面问题
- **滚动不工作**: 尝试在不同区域使用鼠标滚轮
- **组件显示不全**: 调整窗口大小或最大化窗口
- **响应缓慢**: 检查系统资源使用情况

### 数据问题
- **读取失败**: 检查Excel文件是否被其他程序占用
- **范围错误**: 确认行列范围设置正确
- **输出位置**: 确认输出范围不与输入范围冲突

## 📈 性能建议

### 优化设置
- **迭代次数**: 建议100-500次，根据数据复杂度调整
- **方案数量**: 建议3-10个，避免过多影响性能
- **数据范围**: 尽量精确设置，避免包含空白区域

### 系统要求
- **内存**: 建议8GB或更多
- **分辨率**: 建议1920x1080或更高
- **Excel版本**: 支持Excel 2016或更新版本

---

## 🎯 v4.1 改进总结

**主要改进**:
- ✅ 新增输出数据范围配置功能
- ✅ 统一滚动界面，移除左右分栏限制
- ✅ 增强窗口响应性和组件自适应
- ✅ 改进网格权重分布
- ✅ 精简分发包，只包含必要文件

**用户体验提升**:
- 更直观的界面布局
- 更流畅的滚动体验
- 更好的窗口适应性
- 更精确的结果输出控制

**版本**: v4.1 改进版  
**发布日期**: 2025年6月26日  
**状态**: ✅ 生产就绪  
**兼容性**: Windows 10/11

🚀 立即体验v4.1的所有改进功能！
